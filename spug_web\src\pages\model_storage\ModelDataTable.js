import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, message, Spin, Space, Modal, Input, Form, Select, Table } from 'antd';
import { SaveOutlined, PlusOutlined, DeleteOutlined, EditOutlined, ReloadOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import http from 'libs/http';
import styles from './ModelDataTable.module.css';

const { Option } = Select;

function ModelDataTable() {
  const { modelName } = useParams();
  const [loading, setLoading] = useState(true);
  const [modelData, setModelData] = useState([]);
  const [editingKey, setEditingKey] = useState('');
  const [hasChanges, setHasChanges] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();

  // 判断是否正在编辑
  const isEditing = (record) => record.key === editingKey;

  // 编辑操作
  const edit = (record) => {
    editForm.setFieldsValue({
      model_name: record.model_name,
      model_version: record.model_version,
      model_size: record.model_size,
      framework: record.framework,
      accuracy: parseFloat(record.accuracy) || '',
      inference_speed: record.inference_speed,
      gpu_memory: record.gpu_memory,
      dataset: record.dataset,
      status: record.status,
    });
    setEditingKey(record.key);
  };

  // 取消编辑
  const cancel = () => {
    setEditingKey('');
  };

  // 保存编辑
  const save = async (key) => {
    try {
      const row = await editForm.validateFields();
      const newData = [...modelData];
      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        const updatedItem = { ...item, ...row };
        newData.splice(index, 1, updatedItem);
        setModelData(newData);
        setEditingKey('');
        setHasChanges(true);

        // 调用API更新数据
        await updateModelData(updatedItem);
      }
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
    }
  };

  // 更新模型数据API调用
  const updateModelData = async (record) => {
    try {
      const updateData = {
        model_name: record.model_name,
        model_version: record.model_version,
        model_size: record.model_size,
        framework: record.framework,
        accuracy: parseFloat(record.accuracy) || null,
        inference_speed: record.inference_speed,
        gpu_memory: record.gpu_memory,
        dataset: record.dataset,
        status: record.status
      };

      await http.put(`/api/model-storage/model-data/${record._original.id}/`, updateData);
      message.success('更新成功');
    } catch (error) {
      message.error('更新失败');
      console.error('Update error:', error);
    }
  };

  // 表格列定义
  const getColumns = () => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        align: 'center',
      },
      {
        title: '模型名称',
        dataIndex: 'model_name',
        key: 'model_name',
        width: 150,
        editable: true,
      },
      {
        title: '模型版本',
        dataIndex: 'model_version',
        key: 'model_version',
        width: 120,
        editable: true,
      },
      {
        title: '模型大小',
        dataIndex: 'model_size',
        key: 'model_size',
        width: 120,
        editable: true,
      },
      {
        title: '框架',
        dataIndex: 'framework',
        key: 'framework',
        width: 100,
        editable: true,
        render: (text) => {
          const frameworkMap = {
            'pytorch': 'PyTorch',
            'tensorflow': 'TensorFlow',
            'onnx': 'ONNX',
            'huggingface': 'Hugging Face'
          };
          return frameworkMap[text] || text;
        }
      },
      {
        title: '准确率',
        dataIndex: 'accuracy',
        key: 'accuracy',
        width: 100,
        editable: true,
        render: (text) => text ? `${text}%` : '待测试'
      },
      {
        title: '推理速度',
        dataIndex: 'inference_speed',
        key: 'inference_speed',
        width: 120,
        editable: true,
      },
      {
        title: 'GPU内存需求',
        dataIndex: 'gpu_memory',
        key: 'gpu_memory',
        width: 120,
        editable: true,
      },
      {
        title: '训练数据集',
        dataIndex: 'dataset',
        key: 'dataset',
        width: 150,
        editable: true,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        editable: true,
        render: (text) => {
          const statusMap = {
            'development': '开发中',
            'testing': '测试中',
            'production': '生产环境',
            'deprecated': '已废弃'
          };
          return statusMap[text] || text;
        }
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 150,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        width: 150,
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        fixed: 'right',
        render: (_, record) => {
          const editable = isEditing(record);
          return editable ? (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => save(record.key)}
                style={{ marginRight: 8 }}
              >
                保存
              </Button>
              <Button type="link" size="small" onClick={cancel}>
                取消
              </Button>
            </Space>
          ) : (
            <Button
              type="link"
              size="small"
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            >
              编辑
            </Button>
          );
        },
      },
    ];
  };

  // 可编辑单元格组件
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    let inputNode;

    if (dataIndex === 'framework') {
      inputNode = (
        <Select style={{ width: '100%' }}>
          <Option value="pytorch">PyTorch</Option>
          <Option value="tensorflow">TensorFlow</Option>
          <Option value="onnx">ONNX</Option>
          <Option value="huggingface">Hugging Face</Option>
        </Select>
      );
    } else if (dataIndex === 'status') {
      inputNode = (
        <Select style={{ width: '100%' }}>
          <Option value="development">开发中</Option>
          <Option value="testing">测试中</Option>
          <Option value="production">生产环境</Option>
          <Option value="deprecated">已废弃</Option>
        </Select>
      );
    } else {
      inputNode = <Input />;
    }

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={[
              {
                required: ['model_name', 'model_version'].includes(dataIndex),
                message: `请输入${title}!`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  // 合并列配置
  const mergedColumns = getColumns().map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === 'accuracy' ? 'number' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  // 获取模型数据
  const fetchModelData = async () => {
    setLoading(true);
    try {
      // 使用专门的模型数据API
      const response = await http.get('/api/model-storage/model-data/', {
        params: { model_name: decodeURIComponent(modelName) }
      });

      // 转换数据格式以适配表格
      const transformedData = response.map((modelData, index) => ({
        key: modelData.id || index,
        id: modelData.id,
        model_name: modelData.model_name,
        model_version: modelData.model_version,
        model_size: modelData.model_size || '未知',
        framework: modelData.framework,
        accuracy: modelData.accuracy || '',
        inference_speed: modelData.inference_speed || '待测试',
        gpu_memory: modelData.gpu_memory || '未知',
        dataset: modelData.dataset || '未指定',
        status: modelData.status,
        created_at: modelData.created_at || '',
        updated_at: modelData.updated_at || '',
        // 保存原始数据用于更新
        _original: modelData
      }));

      setModelData(transformedData);
    } catch (error) {
      message.error('获取模型数据失败');
      console.error('Fetch model data error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 辅助函数：从模型类型获取框架
  const getFrameworkFromType = (modelType) => {
    const typeMap = {
      'inference': 'PyTorch',
      'training': 'TensorFlow',
      'fine_tuning': 'PyTorch',
      'pre-training': 'TensorFlow',
      'optimization': 'ONNX'
    };
    return typeMap[modelType] || 'Unknown';
  };

  // 辅助函数：获取状态显示
  const getStatusDisplay = (testStatus) => {
    const statusMap = {
      'pending': '待开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'blocked': '阻塞中',
      'delayed': '已延期'
    };
    return statusMap[testStatus] || '未知';
  };





  // 添加新记录
  const handleAddRecord = async () => {
    try {
      const values = await form.validateFields();

      const newRecordData = {
        model_name: values.model_name,
        model_version: values.model_version || 'v1.0',
        model_size: values.model_size || '',
        framework: values.framework || 'pytorch',
        gpu_memory: values.gpu_memory || '',
        dataset: values.dataset || '',
        status: 'development',
        created_by: 'current_user' // 这里应该从用户上下文获取
      };

      // 调用API创建新记录
      const response = await http.post('/api/model-storage/model-data/', newRecordData);

      message.success('添加成功');
      setAddModalVisible(false);
      form.resetFields();
      fetchModelData(); // 重新获取数据以显示新记录

    } catch (error) {
      if (error.response && error.response.data && error.response.data.error) {
        message.error(error.response.data.error);
      } else {
        message.error('添加失败');
      }
      console.error('Add record error:', error);
    }
  };

  // 导出数据
  const exportData = () => {
    const csvContent = [
      MODEL_FIELDS.map(f => f.title).join(','),
      ...modelData.map(record => 
        MODEL_FIELDS.map(f => record[f.key] || '').join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${modelName}_model_data.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    fetchModelData();
  }, [modelName]);

  return (
    <div className={styles.modelDataTable}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <EditOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>模型数据表格 - {decodeURIComponent(modelName)}</span>
          </div>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchModelData}
              disabled={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setAddModalVisible(true)}
            >
              添加记录
            </Button>

          </Space>
        }
      >
        <Form form={editForm} component={false}>
          <Table
            components={{
              body: {
                cell: EditableCell,
              },
            }}
            bordered
            dataSource={modelData}
            columns={mergedColumns}
            rowClassName="editable-row"
            pagination={{
              onChange: cancel,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            loading={loading}
            scroll={{ x: 1500 }}
          />
        </Form>
      </Card>

      {/* 添加记录模态框 */}
      <Modal
        title="添加新记录"
        visible={addModalVisible}
        onOk={handleAddRecord}
        onCancel={() => {
          setAddModalVisible(false);
          form.resetFields();
        }}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="model_name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="请输入模型名称" />
          </Form.Item>
          <Form.Item name="model_version" label="模型版本">
            <Input placeholder="例如: v1.0" />
          </Form.Item>
          <Form.Item name="model_size" label="模型大小">
            <Input placeholder="例如: 7B, 13B" />
          </Form.Item>
          <Form.Item name="framework" label="框架">
            <Select placeholder="请选择框架">
              <Option value="PyTorch">PyTorch</Option>
              <Option value="TensorFlow">TensorFlow</Option>
              <Option value="ONNX">ONNX</Option>
              <Option value="Hugging Face">Hugging Face</Option>
            </Select>
          </Form.Item>
          <Form.Item name="gpu_memory" label="GPU内存需求">
            <Input placeholder="例如: 8GB, 16GB" />
          </Form.Item>
          <Form.Item name="dataset" label="训练数据集">
            <Input placeholder="请输入数据集名称" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default ModelDataTable;
