import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Card, 
  Tree, 
  Spin, 
  Alert, 
  Tag, 
  Input, 
  Space,
  Row,
  Col,
  Typography,
  Breadcrumb,
  Button,
  Empty
} from 'antd';
import { 
  FolderOutlined, 
  GlobalOutlined, 
  HomeOutlined, 
  DiffOutlined, 
  ReloadOutlined, 
  CloudOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { http } from '../../libs';
import styles from './FileDetailCompare.module.less';

const { Search } = Input;
const { Title } = Typography;

export default function FileDetailCompare() {
  const [loading, setLoading] = useState(false);
  const [localTreeData, setLocalTreeData] = useState([]);
  const [remoteTreeData, setRemoteTreeData] = useState([]);
  const [localExpandedKeys, setLocalExpandedKeys] = useState(['local_root']);
  const [remoteExpandedKeys, setRemoteExpandedKeys] = useState(['remote_root']);
  const [searchValue, setSearchValue] = useState('');
  const [error, setError] = useState(null);
  const [diffStatus, setDiffStatus] = useState({
    local: {},
    remote: {}
  });
  
  // 文档统计功能 - 在新标签页打开
  const handleDocStatsModal = () => {
    // 在新标签页中打开文档统计页面
    const url = '/model-storage/doc-statistics';
    window.open(url, '_blank');
  };

  const updateTreeData = (list, key, children) => {
    return list.map((node) => {
      if (node.key === key) {
        return {
          ...node,
          children,
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children),
        };
      }
      return node;
    });
  };

  const config = useMemo(() => ({
    basePath: new URLSearchParams(window.location.search).get('basePath') || '/HDD_Raid/SVN_MODEL_REPO',
    remoteUrl: new URLSearchParams(window.location.search).get('remoteUrl') || 'http://10.63.30.93/GPU_MODEL_REPO/01.DEV/',
    compareId: new URLSearchParams(window.location.search).get('compareId'),
  }), []);

  const loadDiffInfo = async () => {
    if (!config.compareId) return;
    try {
      const res = await http.get(`/api/model-storage/compare/${config.compareId}/diff-status/`);
      const { local = {}, remote = {} } = res.data || {};
      setDiffStatus({ local, remote });
    } catch (error) {
      // 加载差异信息失败
    }
  };

  const getGitStatusTag = useCallback((status) => {
    if (!status) return null;

    const statusMap = {
      'A': { color: 'success', text: '新增' },
      'M': { color: 'warning', text: '修改' },
      'D': { color: 'error', text: '删除' },
      'R': { color: 'processing', text: '重命名' },
    };

    return statusMap[status] ? (
      <Tag color={statusMap[status].color} style={{ marginLeft: 8 }}>
        {statusMap[status].text}
      </Tag>
    ) : null;
  }, []);

  const buildErrorNode = useCallback((source) => ({
    title: source === 'local' ? '📁 本地仓库 (加载失败)' : '🌐 远程仓库 (加载失败)',
    key: `${source}_root`,
    icon: source === 'local' ? <FolderOutlined /> : <GlobalOutlined />,
    children: [{
      title: '❌ 加载失败，请点击刷新重试',
      key: `${source}_error_msg`,
      isLeaf: true
    }]
  }), []);

  const buildTreeNode = useCallback((item, index, source) => {
    const isFolder = item.data?.type === 'folder';
    const baseTitle = item.data?.name || item.title || `Item_${index}`;
    const statusTag = getGitStatusTag(item.data?.status);
    
    // 处理图标：如果是Unicode编码，进行解码
    let iconDisplay = item.icon;
    if (typeof item.icon === 'string' && item.icon.includes('\\u')) {
      try {
        iconDisplay = JSON.parse(`"${item.icon}"`);
      } catch (e) {
        iconDisplay = item.icon;
      }
    }
    
    const node = {
      title: (
        <span>
          {iconDisplay} {baseTitle}
          {statusTag}
        </span>
      ),
      key: item.key || `${source}_${index}`,
      isLeaf: !isFolder,
      data: {
        ...item.data,
        source: source
      }
    };
    
    return node;
  }, [getGitStatusTag]);

  const buildLocalTree = useCallback((response) => {
    const result = [{
      title: '📁 本地仓库',
      key: 'local_root',
      children: (response?.children || [])
        .slice(0, 20)
        .map((item, index) => {
          const node = buildTreeNode(item, index, 'local');
          return node;
        })
    }];
    
    return result;
  }, [buildTreeNode]);

  const buildRemoteTree = useCallback((response) => {
    const data = Array.isArray(response) ? response : response?.children || [];
    
    const result = [{
      title: '🌐 远程仓库',
      key: 'remote_root',
      children: data
        .slice(0, 20)
        .map((item, index) => {
          const node = buildTreeNode(item, index, 'remote');
          return node;
        })
    }];
    
    return result;
  }, [buildTreeNode]);

  const loadInitialData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [localResponse, remoteResponse] = await Promise.all([
        http.get('/api/model-storage/lazy-load-tree/', {
          params: { path: config.basePath, root: true }
        }),
        http.get('/api/model-storage/remote-lazy-load/', {
          params: { path: config.remoteUrl }
        })
      ]);

      const localTree = buildLocalTree(localResponse);
      const remoteTree = buildRemoteTree(remoteResponse);

      setLocalTreeData(localTree);
      setRemoteTreeData(remoteTree);
    } catch (error) {
      setError(error.message || '加载数据失败');
      setLocalTreeData([buildErrorNode('local')]);
      setRemoteTreeData([buildErrorNode('remote')]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 监听数据变化
  }, [localTreeData, remoteTreeData, loading, error]);

  useEffect(() => {
    if (config.basePath && config.remoteUrl) {
      loadInitialData();
    }
    
    if (config.compareId) {
      loadDiffInfo();
    }
  }, []);

  const onLoadData = useCallback(async (treeNode) => {
    const { key, data } = treeNode;
    if (!data?.path || (treeNode.children && treeNode.children.length > 0)) {
      return;
    }

    try {
      const isLocal = key.startsWith('local_');
      
      const response = await http.get(
        isLocal ? '/api/model-storage/lazy-load-tree/' : '/api/model-storage/remote-lazy-load/',
        { params: { path: data.path } }
      );
      
      const responseData = response?.data || response?.children || response || [];
      
      const children = responseData?.map((item, index) => {
        const childNode = buildTreeNode(item, index, isLocal ? 'local' : 'remote');
        return childNode;
      }) || [];
      
      if (isLocal) {
        setLocalTreeData(origin => updateTreeData(origin, key, children));
      } else {
        setRemoteTreeData(origin => updateTreeData(origin, key, children));
      }
      
    } catch (error) {
      // 加载子节点失败
    }
  }, [buildTreeNode]);

  const filterTreeNode = useCallback((node, searchValue) => {
    const nodeTitle = node.title?.props?.children?.[0] || node.title;
    if (typeof nodeTitle === 'string') {
      return nodeTitle.toLowerCase().includes(searchValue.toLowerCase());
    }
    return false;
  }, []);

  const onSelect = useCallback((selectedKeys, info) => {
    // 说明：移除了此处的自动展开/折叠逻辑，以避免和 onExpand 事件冲突。
    // 请通过点击节点旁的 > 箭头来展开。
  }, []);



  return (
    <div style={{ padding: '24px', minHeight: '100vh', background: '#f0f2f5' }}>
      {/* 页面头部 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <HomeOutlined />
              <span style={{ marginLeft: '8px' }}>首页</span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <DiffOutlined />
              <span style={{ marginLeft: '8px' }}>文件对比</span>
            </Breadcrumb.Item>
          </Breadcrumb>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Title level={3} style={{ margin: 0 }}>
                <DiffOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
                文件树对比
              </Title>
              <p style={{ margin: '8px 0 0 0', color: '#666' }}>
                对比本地仓库与远程仓库的文件差异
              </p>
            </div>
            <Space>
              <Button 
                icon={<BarChartOutlined />} 
                onClick={handleDocStatsModal}
                loading={false}
                style={{
                  borderRadius: '8px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  color: 'white',
                  fontWeight: '500'
                }}
              >
                文档统计
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadInitialData}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 主要内容 */}
      <Card className={styles.container} style={{ minHeight: 'calc(100vh - 200px)' }}>
        {error && <Alert type="error" message={error} className={styles.alert} />}
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Search
              placeholder="搜索文件或路径..."
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
              style={{ width: 400 }}
            />
            <Space>
              <span style={{ color: '#666' }}>
                本地: {config.basePath}
              </span>
              <span style={{ color: '#666' }}>
                远程: {config.remoteUrl}
              </span>
            </Space>
          </div>
          
          <Row gutter={16}>
            <Col span={12}>
              {/* 本地仓库 */}
              <Card 
                title={<><FolderOutlined /> 本地仓库: {config.basePath}</>}
                size="small" 
                style={{ height: '75vh', overflow: 'auto' }}
              >

                {localTreeData && localTreeData.length > 0 ? (
                  <Tree
                    showIcon
                    defaultExpandAll={false}
                    defaultExpandedKeys={['local_root']}
                    loadData={onLoadData}
                    treeData={localTreeData}
                    onExpand={(expandedKeys, info) => {
                      setLocalExpandedKeys(expandedKeys);
                    }}
                    expandedKeys={localExpandedKeys}
                    onSelect={(selectedKeys, info) => {
                      onSelect(selectedKeys, info);
                    }}
                  />
                ) : (
                  <Empty 
                    image={Empty.PRESENTED_IMAGE_SIMPLE} 
                    description={loading ? '加载中...' : '暂无数据'} 
                  />
                )}
              </Card>
            </Col>
            
            <Col span={12}>
              {/* 远程仓库 */}
              <Card 
                title={<><CloudOutlined /> 远程仓库: {config.remoteUrl}</>}
                size="small" 
                style={{ height: '75vh', overflow: 'auto' }}
              >

                {remoteTreeData && remoteTreeData.length > 0 ? (
                  <Tree
                    showIcon
                    defaultExpandAll={false}
                    defaultExpandedKeys={['remote_root']}
                    loadData={onLoadData}
                    treeData={remoteTreeData}
                    onExpand={(expandedKeys, info) => {
                      setRemoteExpandedKeys(expandedKeys);
                    }}
                    expandedKeys={remoteExpandedKeys}
                    onSelect={(selectedKeys, info) => {
                      onSelect(selectedKeys, info);
                    }}
                  />
                ) : (
                  <Empty 
                    image={Empty.PRESENTED_IMAGE_SIMPLE} 
                    description={loading ? '加载中...' : '暂无数据'} 
                  />
                )}
              </Card>
            </Col>
          </Row>
        </Space>
      </Card>
    </div>
  );
}