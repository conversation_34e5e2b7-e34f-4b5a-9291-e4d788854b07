#!/usr/bin/env python3
"""
测试性能测试数据API的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:9999/api/model-storage"

def test_get_performance_data():
    """测试获取性能测试数据列表"""
    print("测试获取性能测试数据列表...")
    try:
        response = requests.get(f"{BASE_URL}/performance-test-data/", params={"token": "1"})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"获取到 {len(data)} 条性能测试数据")
            for item in data[:2]:  # 只显示前2条
                print(f"- {item.get('model_name', 'N/A')} - {item.get('filename', 'N/A')}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_get_performance_data_by_model():
    """测试按模型名称获取性能测试数据"""
    model_name = "test-model"
    print(f"\n测试获取模型 {model_name} 的性能测试数据...")
    try:
        response = requests.get(
            f"{BASE_URL}/performance-test-data/", 
            params={"model_name": model_name, "token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"获取到 {len(data)} 条 {model_name} 的性能测试数据")
            for item in data:
                print(f"- {item.get('filename', 'N/A')} - {item.get('success_requests', 0)} 请求")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_create_performance_data():
    """测试创建新性能测试数据"""
    print("\n测试创建新性能测试数据...")
    new_data = {
        "model_name": "test-model",
        "filename": "test_performance.json",
        "success_requests": 1000,
        "benchmark_duration": 60.5,
        "input_tokens": 50000,
        "output_tokens": 25000,
        "request_throughput": 16.53,
        "output_token_throughput": 413.22,
        "total_token_throughput": 1239.67,
        "avg_ttft": 245.6,
        "median_ttft": 230.1,
        "p99_ttft": 450.8,
        "avg_tpot": 12.3,
        "median_tpot": 11.8,
        "p99_tpot": 18.9
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/performance-test-data/",
            json=new_data,
            params={"token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功创建性能测试数据: {data.get('filename')} (ID: {data.get('id')})")
            return data.get('id')
        else:
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_update_performance_data(data_id):
    """测试更新性能测试数据"""
    if not data_id:
        print("\n跳过更新测试（没有有效的数据ID）")
        return
        
    print(f"\n测试更新性能测试数据 ID: {data_id}...")
    update_data = {
        "model_name": "test-model-updated",
        "filename": "test_performance_updated.json",
        "success_requests": 1200,
        "benchmark_duration": 65.0
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/performance-test-data/{data_id}/",
            json=update_data,
            params={"token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功更新性能测试数据: {data.get('filename')}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_delete_performance_data(data_id):
    """测试删除性能测试数据"""
    if not data_id:
        print("\n跳过删除测试（没有有效的数据ID）")
        return
        
    print(f"\n测试删除性能测试数据 ID: {data_id}...")
    try:
        response = requests.delete(
            f"{BASE_URL}/performance-test-data/{data_id}/",
            params={"token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("成功删除性能测试数据")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("=== 性能测试数据API测试 ===")
    
    # 测试获取所有数据
    test_get_performance_data()
    
    # 测试按模型名称获取数据
    test_get_performance_data_by_model()
    
    # 测试创建数据
    created_id = test_create_performance_data()
    
    # 测试更新数据
    test_update_performance_data(created_id)
    
    # 测试删除数据
    test_delete_performance_data(created_id)
    
    print("\n=== 测试完成 ===")
