import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, message, Space, Spin, Progress, Modal, Input, Table } from 'antd';
import { ArrowLeftOutlined, DownloadOutlined, ReloadOutlined, Bar<PERSON><PERSON>Outlined, SaveOutlined, CopyOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import * as XLSX from 'xlsx';
import { http } from 'libs';

// 智能提取模板 - 基于实际日志格式，完整版本
const LOG_EXTRACTION_TEMPLATE = {
  patterns: [
    { key: '成功请求数', type: 'number', pattern: /Successful requests:\s*(\d+)/ },
    { key: '基准时长(s)', type: 'number', pattern: /Benchmark duration \(s\):\s*([\d.]+)/ },
    { key: '输入Token总数', type: 'number', pattern: /Total input tokens:\s*(\d+)/ },
    { key: '生成Token总数', type: 'number', pattern: /Total generated tokens:\s*(\d+)/ },
    { key: '请求吞吐量(req/s)', type: 'number', pattern: /Request throughput \(req\/s\):\s*([\d.]+)/ },
    { key: '输出Token吞吐量(tok/s)', type: 'number', pattern: /Output token throughput \(tok\/s\):\s*([\d.]+)/ },
    { key: '总Token吞吐量(tok/s)', type: 'number', pattern: /Total Token throughput \(tok\/s\):\s*([\d.]+)/ },
    { key: '平均TTFT(ms)', type: 'number', pattern: /Mean TTFT \(ms\):\s*([\d.]+)/ },
    { key: '中位数TTFT(ms)', type: 'number', pattern: /Median TTFT \(ms\):\s*([\d.]+)/ },
    { key: 'P99 TTFT(ms)', type: 'number', pattern: /P99 TTFT \(ms\):\s*([\d.]+)/ },
    { key: '平均TPOT(ms)', type: 'number', pattern: /Mean TPOT \(ms\):\s*([\d.]+)/ },
    { key: '中位数TPOT(ms)', type: 'number', pattern: /Median TPOT \(ms\):\s*([\d.]+)/ },
    { key: 'P99 TPOT(ms)', type: 'number', pattern: /P99 TPOT \(ms\):\s*([\d.]+)/ },
    { key: '平均ITL(ms)', type: 'number', pattern: /Mean ITL \(ms\):\s*([\d.]+)/ },
    { key: '中位数ITL(ms)', type: 'number', pattern: /Median ITL \(ms\):\s*([\d.]+)/ },
    { key: 'P99 ITL(ms)', type: 'number', pattern: /P99 ITL \(ms\):\s*([\d.]+)/ }
  ]
};

// 表格列定义 - 完整版本
const TABLE_COLUMNS = [
  {
    title: '文件名',
    dataIndex: '文件名',
    key: '文件名',
    width: 200,
    sorter: (a, b) => (a.文件名 || '').localeCompare(b.文件名 || ''),
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '成功请求数',
    dataIndex: '成功请求数',
    key: '成功请求数',
    width: 120,
    sorter: (a, b) => (a.成功请求数 || 0) - (b.成功请求数 || 0),
    align: 'right',
  },
  {
    title: '基准时长(s)',
    dataIndex: '基准时长(s)',
    key: '基准时长(s)',
    width: 120,
    sorter: (a, b) => parseFloat(a['基准时长(s)'] || 0) - parseFloat(b['基准时长(s)'] || 0),
    align: 'right',
  },
  {
    title: '输入Token总数',
    dataIndex: '输入Token总数',
    key: '输入Token总数',
    width: 140,
    sorter: (a, b) => (a.输入Token总数 || 0) - (b.输入Token总数 || 0),
    align: 'right',
  },
  {
    title: '生成Token总数',
    dataIndex: '生成Token总数',
    key: '生成Token总数',
    width: 140,
    sorter: (a, b) => (a.生成Token总数 || 0) - (b.生成Token总数 || 0),
    align: 'right',
  },
  {
    title: '请求吞吐量(req/s)',
    dataIndex: '请求吞吐量(req/s)',
    key: '请求吞吐量(req/s)',
    width: 150,
    sorter: (a, b) => parseFloat(a['请求吞吐量(req/s)'] || 0) - parseFloat(b['请求吞吐量(req/s)'] || 0),
    align: 'right',
  },
  {
    title: '输出Token吞吐量(tok/s)',
    dataIndex: '输出Token吞吐量(tok/s)',
    key: '输出Token吞吐量(tok/s)',
    width: 180,
    sorter: (a, b) => parseFloat(a['输出Token吞吐量(tok/s)'] || 0) - parseFloat(b['输出Token吞吐量(tok/s)'] || 0),
    align: 'right',
  },
  {
    title: '总Token吞吐量(tok/s)',
    dataIndex: '总Token吞吐量(tok/s)',
    key: '总Token吞吐量(tok/s)',
    width: 170,
    sorter: (a, b) => parseFloat(a['总Token吞吐量(tok/s)'] || 0) - parseFloat(b['总Token吞吐量(tok/s)'] || 0),
    align: 'right',
  },
  {
    title: '平均TTFT(ms)',
    dataIndex: '平均TTFT(ms)',
    key: '平均TTFT(ms)',
    width: 130,
    sorter: (a, b) => parseFloat(a['平均TTFT(ms)'] || 0) - parseFloat(b['平均TTFT(ms)'] || 0),
    align: 'right',
  },
  {
    title: '中位数TTFT(ms)',
    dataIndex: '中位数TTFT(ms)',
    key: '中位数TTFT(ms)',
    width: 150,
    sorter: (a, b) => parseFloat(a['中位数TTFT(ms)'] || 0) - parseFloat(b['中位数TTFT(ms)'] || 0),
    align: 'right',
  },
  {
    title: 'P99 TTFT(ms)',
    dataIndex: 'P99 TTFT(ms)',
    key: 'P99 TTFT(ms)',
    width: 130,
    sorter: (a, b) => parseFloat(a['P99 TTFT(ms)'] || 0) - parseFloat(b['P99 TTFT(ms)'] || 0),
    align: 'right',
  },
  {
    title: '平均TPOT(ms)',
    dataIndex: '平均TPOT(ms)',
    key: '平均TPOT(ms)',
    width: 130,
    sorter: (a, b) => parseFloat(a['平均TPOT(ms)'] || 0) - parseFloat(b['平均TPOT(ms)'] || 0),
    align: 'right',
  },
  {
    title: '中位数TPOT(ms)',
    dataIndex: '中位数TPOT(ms)',
    key: '中位数TPOT(ms)',
    width: 150,
    sorter: (a, b) => parseFloat(a['中位数TPOT(ms)'] || 0) - parseFloat(b['中位数TPOT(ms)'] || 0),
    align: 'right',
  },
  {
    title: 'P99 TPOT(ms)',
    dataIndex: 'P99 TPOT(ms)',
    key: 'P99 TPOT(ms)',
    width: 130,
    sorter: (a, b) => parseFloat(a['P99 TPOT(ms)'] || 0) - parseFloat(b['P99 TPOT(ms)'] || 0),
    align: 'right',
  },
  {
    title: '平均ITL(ms)',
    dataIndex: '平均ITL(ms)',
    key: '平均ITL(ms)',
    width: 120,
    sorter: (a, b) => parseFloat(a['平均ITL(ms)'] || 0) - parseFloat(b['平均ITL(ms)'] || 0),
    align: 'right',
  },
  {
    title: '中位数ITL(ms)',
    dataIndex: '中位数ITL(ms)',
    key: '中位数ITL(ms)',
    width: 140,
    sorter: (a, b) => parseFloat(a['中位数ITL(ms)'] || 0) - parseFloat(b['中位数ITL(ms)'] || 0),
    align: 'right',
  },
  {
    title: 'P99 ITL(ms)',
    dataIndex: 'P99 ITL(ms)',
    key: 'P99 ITL(ms)',
    width: 120,
    sorter: (a, b) => parseFloat(a['P99 ITL(ms)'] || 0) - parseFloat(b['P99 ITL(ms)'] || 0),
    align: 'right',
  }
];

const AntdTableAnalysisPage = () => {
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [progress, setProgress] = useState(0);
  const [analysisParams, setAnalysisParams] = useState(null);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [saveName, setSaveName] = useState('');
  const [saveDescription, setSaveDescription] = useState('');
  const [saving, setSaving] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);

  // 获取URL参数
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const hostId = urlParams.get('hostId');
    const containerName = urlParams.get('containerName');
    const selectedFiles = urlParams.get('selectedFiles');

    console.log('URL参数:', { hostId, containerName, selectedFiles });

    if (hostId && containerName && selectedFiles) {
      try {
        const parsedFiles = JSON.parse(decodeURIComponent(selectedFiles));
        console.log('解析后的文件列表:', parsedFiles);

        const params = {
          hostId,
          containerName,
          selectedFiles: parsedFiles
        };
        setAnalysisParams(params);
        processFiles(params);
      } catch (error) {
        console.error('解析URL参数失败:', error);
      }
    } else {
      console.log('缺少必要的URL参数');
    }
  }, []);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+C 复制表格数据
      if (event.ctrlKey && event.key === 'c' && !event.target.closest('input, textarea')) {
        event.preventDefault();
        if (selectedRows.length > 0) {
          copySelectedData();
        } else {
          copyTableData();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedRows, extractedData]);

  // 从日志内容中提取数据
  const extractDataFromContent = useCallback((content, filename) => {
    console.log('开始提取数据，文件名:', filename);
    console.log('日志内容长度:', content.length);

    const result = { 文件名: filename };

    // 使用模板模式提取数据
    LOG_EXTRACTION_TEMPLATE.patterns.forEach(({ key, type, pattern }) => {
      const match = content.match(pattern);
      console.log(`匹配 ${key}:`, match);

      if (match && match[1]) {
        let value = match[1].trim();
        if (type === 'number') {
          // 提取数字，去除逗号等格式字符
          const numMatch = value.match(/[\d.,]+/);
          value = numMatch ? parseFloat(numMatch[0].replace(/,/g, '')) : 0;
        }
        result[key] = value;
        console.log(`${key} = ${value}`);
      } else {
        result[key] = type === 'number' ? 0 : '-';
        console.log(`${key} 未匹配到，使用默认值`);
      }
    });

    console.log('最终提取结果:', result);
    return result;
  }, []);

  // 处理后端解析的数据
  const processBackendData = useCallback((parsedData, filename) => {
    if (parsedData && typeof parsedData === 'object') {
      return { ...parsedData, key: filename }; // 添加key属性用于Table组件
    }

    const defaultResult = { 文件名: filename, key: filename };
    LOG_EXTRACTION_TEMPLATE.patterns.forEach(({ key, type }) => {
      defaultResult[key] = type === 'number' ? 0 : '-';
    });

    return defaultResult;
  }, []);

  // 处理文件分析
  const processFiles = async (params = analysisParams) => {
    console.log('开始处理文件，参数:', params);

    if (!params) {
      console.log('没有分析参数，退出');
      return;
    }

    setLoading(true);
    setProgress(0);
    setExtractedData([]);

    try {
      const { hostId, containerName, selectedFiles } = params;
      console.log('分析参数详情:', { hostId, containerName, selectedFiles });

      const results = [];

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        console.log(`处理第 ${i + 1} 个文件:`, file);
        setProgress(Math.round(((i + 1) / selectedFiles.length) * 100));

        try {
          const apiPayload = {
            log_paths: [file.fullPath || file.path],
            host_id: parseInt(hostId),
            use_docker: true,
            container_name: containerName
          };

          console.log(`API请求参数:`, apiPayload);

          // 使用正确的API接口获取文件内容
          const response = await http.post('/api/exec/remote-log-fetch-multiple/', apiPayload);

          console.log(`API响应:`, response);

          if (response.success && response.results && response.results.length > 0) {
            const fileResult = response.results[0];
            console.log(`文件结果:`, fileResult);

            if (fileResult.success && fileResult.content) {
              // 解析日志内容
              const extractedData = extractDataFromContent(fileResult.content, file.name);
              console.log(`提取的数据:`, extractedData);

              const processedData = processBackendData(extractedData, file.name);
              console.log(`处理后的数据:`, processedData);

              results.push(processedData);
            } else {
              console.error(`文件 ${file.name} 获取失败:`, fileResult.error);
              const defaultData = processBackendData(null, file.name);
              results.push(defaultData);
            }
          } else {
            console.error(`API响应异常:`, response);
            const defaultData = processBackendData(null, file.name);
            results.push(defaultData);
          }
        } catch (error) {
          console.error(`处理文件 ${file.name} 失败:`, error);
          const defaultData = processBackendData(null, file.name);
          results.push(defaultData);
        }
      }

      console.log('最终结果数组:', results);
      setExtractedData(results);
      message.success(`成功分析 ${results.length} 个文件`);
    } catch (error) {
      console.error('分析失败:', error);
      message.error('分析失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };

  // 复制表格数据
  const copyTableData = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可复制');
      return;
    }

    try {
      const headers = TABLE_COLUMNS.map(col => col.title).join('\t');
      const rows = extractedData.map(row =>
        TABLE_COLUMNS.map(col => row[col.dataIndex] || '').join('\t')
      ).join('\n');
      const content = headers + '\n' + rows;

      navigator.clipboard.writeText(content).then(() => {
        message.success('已复制表格数据到剪贴板');
      }).catch(err => {
        message.error('复制失败: ' + err.message);
      });
    } catch (error) {
      message.error('复制失败，请重试');
    }
  };

  // 复制选中行数据
  const copySelectedData = () => {
    if (selectedRows.length === 0) {
      message.warning('请先选择要复制的行');
      return;
    }

    try {
      const headers = TABLE_COLUMNS.map(col => col.title).join('\t');
      const rows = selectedRows.map(row =>
        TABLE_COLUMNS.map(col => row[col.dataIndex] || '').join('\t')
      ).join('\n');
      const content = headers + '\n' + rows;

      navigator.clipboard.writeText(content).then(() => {
        message.success(`已复制 ${selectedRows.length} 行数据到剪贴板`);
      }).catch(err => {
        message.error('复制失败: ' + err.message);
      });
    } catch (error) {
      message.error('复制失败，请重试');
    }
  };

  // 导出数据到Excel
  const exportData = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      const worksheet = XLSX.utils.json_to_sheet(extractedData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '基准测试结果');

      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `基准测试分析结果_${timestamp}.xlsx`;

      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('数据导出成功');
    } catch (error) {
      message.error('导出失败: ' + error.message);
    }
  };

  // 保存结果记录
  const handleSave = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可保存');
      return;
    }

    const timestamp = new Date().toLocaleString('zh-CN');
    const defaultName = `基准测试分析结果_${timestamp}`;
    setSaveName(defaultName);
    setSaveDescription(`包含 ${extractedData.length} 个文件的基准测试分析结果`);
    setSaveModalVisible(true);
  };

  // 确认保存
  const confirmSave = async () => {
    if (!saveName.trim()) {
      message.error('请输入结果名称');
      return;
    }

    setSaving(true);
    try {
      const saveData = {
        plan_name: saveName.trim(),
        gpu_name: saveDescription.trim() || '',
        source_type: 'log_extraction',
        metrics: extractedData.map(item => ({
          label: `${item.文件名} - ${item.模型名称 || ''}`,
          value: item.推理吞吐量,
          unit: item.单位 || 'req/s',
          confidence: 1.0,
          category: 'benchmark',
          file_name: item.文件名,
          throughput: item.推理吞吐量,
          latency: item.延迟
        })),
        raw_log: JSON.stringify(extractedData, null, 2),
        total_metrics: extractedData.length,
        confirmed_metrics: extractedData.length,
        ai_confidence: 1.0,
        source: 'antd_table_analysis',
        log_source_host: analysisParams && analysisParams.hostId,
        log_source_path: analysisParams && analysisParams.containerName,
        log_extraction_method: 'antd_table_analysis'
      };

      await http.post('/api/exec/test-results/', saveData);
      message.success('结果保存成功');
      setSaveModalVisible(false);
      setSaveName('');
      setSaveDescription('');
    } catch (error) {
      const errorMsg = (error.response && error.response.data && (error.response.data.error || error.response.data.message)) || error.message || '未知错误';
      message.error('保存失败: ' + errorMsg);
    } finally {
      setSaving(false);
    }
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      console.log('全选状态:', selected, selectedRows, changeRows);
    },
  };

  return (
    <Card
      title="基准测试结果分析"
      extra={
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => history.goBack()}
          >
            返回
          </Button>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={() => processFiles()}
            loading={loading}
            disabled={!analysisParams}
          >
            重新分析
          </Button>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={extractedData.length === 0}
          >
            保存结果
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={exportData}
            disabled={extractedData.length === 0}
          >
            导出Excel
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={copyTableData}
            disabled={extractedData.length === 0}
          >
            复制全部
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={copySelectedData}
            disabled={selectedRows.length === 0}
          >
            复制选中 ({selectedRows.length})
          </Button>
        </Space>
      }
    >
      {loading && (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Progress percent={progress} status="active" />
            <p>正在分析日志文件... ({progress}%)</p>
          </div>
        </div>
      )}
      
      {!loading && extractedData.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <BarChartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <p>等待分析数据...</p>
        </div>
      )}

      {!loading && extractedData.length > 0 && (
        <div>
          <div style={{
            marginBottom: 16,
            padding: '12px 16px',
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
            fontSize: '14px',
            color: '#52c41a'
          }}>
            💡 提示：点击列标题可以排序；勾选行后可以复制选中数据；支持多列排序（按住Shift点击多个列标题）
          </div>
          
          <Table
            columns={TABLE_COLUMNS}
            dataSource={extractedData}
            rowSelection={rowSelection}
            scroll={{ x: 1400, y: 600 }}
            size="small"
            bordered
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              pageSizeOptions: ['10', '20', '50', '100'],
              defaultPageSize: 20,
            }}
            onChange={(pagination, filters, sorter, extra) => {
              console.log('表格变化:', { pagination, filters, sorter, extra });
            }}
          />
        </div>
      )}

      {/* 保存结果模态框 */}
      <Modal
        title="保存分析结果"
        open={saveModalVisible}
        onOk={confirmSave}
        onCancel={() => setSaveModalVisible(false)}
        confirmLoading={saving}
        okText="保存"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <label>结果名称：</label>
          <Input
            value={saveName}
            onChange={(e) => setSaveName(e.target.value)}
            placeholder="请输入结果名称"
            style={{ marginTop: 8 }}
          />
        </div>
        <div>
          <label>结果描述：</label>
          <Input.TextArea
            value={saveDescription}
            onChange={(e) => setSaveDescription(e.target.value)}
            placeholder="请输入结果描述（可选）"
            rows={3}
            style={{ marginTop: 8 }}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default AntdTableAnalysisPage;
