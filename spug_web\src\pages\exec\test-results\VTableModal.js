import React, { useState, useEffect, useRef } from 'react';
import { Mo<PERSON>, Card, Button, Space, message, Tag, Descriptions } from 'antd';
import { BarChartOutlined, DownloadOutlined, ReloadOutlined, CloseOutlined } from '@ant-design/icons';
import * as VTable from '@visactor/vtable';

// VTable 列配置
const TABLE_COLUMNS = [
  {
    field: 'filename',
    title: '文件名',
    width: 200,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f2f5' },
    style: { textAlign: 'left' }
  },
  {
    field: 'successful_requests',
    title: '成功请求数',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#e6f7ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'benchmark_duration',
    title: '基准时长(s)',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_input_tokens',
    title: '输入Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_generated_tokens',
    title: '生成Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'request_throughput',
    title: '请求吞吐量(req/s)',
    width: 150,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'output_token_throughput',
    title: '输出Token吞吐量(tok/s)',
    width: 180,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'total_token_throughput',
    title: '总Token吞吐量(tok/s)',
    width: 170,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'mean_ttft',
    title: '平均TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'median_ttft',
    title: '中位TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'p99_ttft',
    title: 'P99 TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'mean_tpot',
    title: '平均TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'median_tpot',
    title: '中位TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'p99_tpot',
    title: 'P99 TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'mean_itl',
    title: '平均ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'median_itl',
    title: '中位ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'p99_itl',
    title: 'P99 ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  }
];

const VTableModal = ({ visible, onCancel, record }) => {
  const [tableInstance, setTableInstance] = useState(null);
  const containerRef = useRef(null);
  const [tableData, setTableData] = useState([]);

  // 处理数据格式
  useEffect(() => {
    if (record && record.results) {
      const processedData = record.results.map((result, index) => {
        // 从结果中提取性能指标
        const extractedData = result.extracted_data || {};
        return {
          filename: record.file_names[index] || `文件${index + 1}`,
          successful_requests: extractedData.successful_requests || '-',
          benchmark_duration: extractedData.benchmark_duration || '-',
          total_input_tokens: extractedData.total_input_tokens || '-',
          total_generated_tokens: extractedData.total_generated_tokens || '-',
          request_throughput: extractedData.request_throughput || '-',
          output_token_throughput: extractedData.output_token_throughput || '-',
          total_token_throughput: extractedData.total_token_throughput || '-',
          mean_ttft: extractedData.mean_ttft || '-',
          median_ttft: extractedData.median_ttft || '-',
          p99_ttft: extractedData.p99_ttft || '-',
          mean_tpot: extractedData.mean_tpot || '-',
          median_tpot: extractedData.median_tpot || '-',
          p99_tpot: extractedData.p99_tpot || '-',
          mean_itl: extractedData.mean_itl || '-',
          median_itl: extractedData.median_itl || '-',
          p99_itl: extractedData.p99_itl || '-'
        };
      });
      setTableData(processedData);
    }
  }, [record]);

  // 初始化VTable
  useEffect(() => {
    if (visible && containerRef.current && tableData.length > 0) {
      // 清理现有表格
      if (tableInstance) {
        tableInstance.release();
      }

      const option = {
        container: containerRef.current,
        columns: TABLE_COLUMNS,
        records: tableData,
        theme: VTable.themes.DEFAULT.extends({
          headerStyle: {
            borderColor: '#e8e8e8',
            borderLineWidth: 1
          },
          bodyStyle: {
            borderColor: '#f0f0f0',
            borderLineWidth: 1
          }
        }),
        widthMode: 'adaptive',
        heightMode: 'adaptive',
        autoWrapText: true,
        hover: {
          highlightMode: 'row'
        },
        select: {
          highlightMode: 'row'
        },
        menu: {
          contextMenuItems: ['复制', '导出选中行', '导出全部数据']
        }
      };

      const table = new VTable.ListTable(option);
      setTableInstance(table);

      // 监听菜单点击事件
      table.on('dropdown_menu_click', (args) => {
        if (args.menuKey === '导出全部数据') {
          exportData();
        }
      });

      return () => {
        if (table) {
          table.release();
        }
      };
    }
  }, [visible, tableData]);

  // 导出数据
  const exportData = () => {
    const csvContent = [
      // CSV 头部
      TABLE_COLUMNS.map(col => col.title).join(','),
      // CSV 数据行
      ...tableData.map(row => 
        TABLE_COLUMNS.map(col => row[col.field] || '').join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${record.name}_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('数据导出成功');
  };

  return (
    <Modal
      title={
        <Space>
          <BarChartOutlined />
          <span>批量分析结果 - VTable高性能展示</span>
          <Tag color="blue">{tableData.length} 条记录</Tag>
        </Space>
      }
      visible={visible}
      onCancel={onCancel}
      footer={[
        <Button key="export" icon={<DownloadOutlined />} onClick={exportData}>
          导出数据
        </Button>,
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>
      ]}
      width="90%"
      style={{ top: 20 }}
      bodyStyle={{ padding: '16px' }}
    >
      {record && (
        <>
          {/* 基本信息 */}
          <Card size="small" style={{ marginBottom: '16px' }}>
            <Descriptions column={3} size="small">
              <Descriptions.Item label="分析名称">{record.name}</Descriptions.Item>
              <Descriptions.Item label="文件数量">{record.file_count}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{record.created_time}</Descriptions.Item>
              <Descriptions.Item label="主机信息">{record.host_info}</Descriptions.Item>
              <Descriptions.Item label="容器名称">{record.container_name || '无'}</Descriptions.Item>
              <Descriptions.Item label="描述">{record.description}</Descriptions.Item>
            </Descriptions>
          </Card>

          {/* VTable表格 */}
          <div 
            ref={containerRef} 
            style={{ 
              height: '500px', 
              width: '100%',
              border: '1px solid #d9d9d9',
              borderRadius: '6px'
            }} 
          />
        </>
      )}
    </Modal>
  );
};

export default VTableModal;
