#!/usr/bin/env python3
"""
测试模型数据API的简单脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:9999/api/model-storage"

def test_get_model_data():
    """测试获取模型数据列表"""
    print("测试获取模型数据列表...")
    try:
        response = requests.get(f"{BASE_URL}/model-data/", params={"token": "1"})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"获取到 {len(data)} 条模型数据")
            for model in data[:2]:  # 只显示前2条
                print(f"- {model['model_name']} {model['model_version']}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_get_model_data_by_name():
    """测试按模型名称过滤"""
    print("\n测试按模型名称过滤...")
    try:
        response = requests.get(f"{BASE_URL}/model-data/", params={
            "token": "1",
            "model_name": "BERT"
        })
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"找到 {len(data)} 条包含'BERT'的模型数据")
            for model in data:
                print(f"- {model['model_name']} {model['model_version']}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_create_model_data():
    """测试创建新模型数据"""
    print("\n测试创建新模型数据...")
    new_model = {
        "model_name": "Test-Model",
        "model_version": "v1.0",
        "model_size": "50M",
        "framework": "pytorch",
        "accuracy": 88.5,
        "inference_speed": "30ms",
        "gpu_memory": "4GB",
        "dataset": "Custom Dataset",
        "status": "testing",
        "description": "测试模型",
        "created_by": "test_user"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/model-data/",
            json=new_model,
            params={"token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"成功创建模型: {data['model_name']} (ID: {data['id']})")
            return data['id']
        else:
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_update_model_data(model_id):
    """测试更新模型数据"""
    if not model_id:
        print("\n跳过更新测试（没有有效的模型ID）")
        return
        
    print(f"\n测试更新模型数据 (ID: {model_id})...")
    update_data = {
        "accuracy": 90.0,
        "status": "production",
        "description": "更新后的测试模型"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/model-data/{model_id}/",
            json=update_data,
            params={"token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功更新模型: {data['model_name']}")
            print(f"新准确率: {data['accuracy']}%")
            print(f"新状态: {data['status_display']}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_delete_model_data(model_id):
    """测试删除模型数据"""
    if not model_id:
        print("\n跳过删除测试（没有有效的模型ID）")
        return
        
    print(f"\n测试删除模型数据 (ID: {model_id})...")
    try:
        response = requests.delete(
            f"{BASE_URL}/model-data/{model_id}/",
            params={"token": "1"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功删除模型: {data['message']}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("开始测试模型数据API...")
    print("=" * 50)
    
    # 测试获取数据
    test_get_model_data()
    test_get_model_data_by_name()
    
    # 测试CRUD操作
    model_id = test_create_model_data()
    test_update_model_data(model_id)
    test_delete_model_data(model_id)
    
    print("\n" + "=" * 50)
    print("API测试完成！")
