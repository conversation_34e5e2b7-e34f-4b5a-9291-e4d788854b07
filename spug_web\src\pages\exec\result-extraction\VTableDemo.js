import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Space, message, Tag } from 'antd';
import { BarChartOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import * as VTable from '@visactor/vtable';

// 模拟的日志分析数据
const DEMO_DATA = [
  {
    filename: 'il1024_ol1024_np1024_mc256.log',
    successful_requests: 8,
    benchmark_duration: 97.66,
    total_input_tokens: 2048,
    total_generated_tokens: 2048,
    request_throughput: 0.08,
    output_token_throughput: 20.97,
    total_token_throughput: 41.94,
    mean_ttft: 334.67,
    median_ttft: 339.56,
    p99_ttft: 343.31,
    mean_tpot: 46.56,
    median_tpot: 46.61,
    p99_tpot: 47.14,
    mean_itl: 46.56,
    median_itl: 44.75,
    p99_itl: 68.00
  },
  {
    filename: 'il1024_ol1024_np16_mc2.log',
    successful_requests: 16,
    benchmark_duration: 45.32,
    total_input_tokens: 1024,
    total_generated_tokens: 1024,
    request_throughput: 0.35,
    output_token_throughput: 22.58,
    total_token_throughput: 45.16,
    mean_ttft: 298.45,
    median_ttft: 301.23,
    p99_ttft: 315.67,
    mean_tpot: 44.23,
    median_tpot: 44.89,
    p99_tpot: 46.78,
    mean_itl: 44.23,
    median_itl: 42.34,
    p99_itl: 58.90
  },
  {
    filename: 'il1024_ol1024_np384_mc32.log',
    successful_requests: 32,
    benchmark_duration: 128.45,
    total_input_tokens: 4096,
    total_generated_tokens: 4096,
    request_throughput: 0.25,
    output_token_throughput: 31.89,
    total_token_throughput: 63.78,
    mean_ttft: 412.34,
    median_ttft: 418.90,
    p99_ttft: 445.67,
    mean_tpot: 31.45,
    median_tpot: 31.78,
    p99_tpot: 33.21,
    mean_itl: 31.45,
    median_itl: 30.12,
    p99_itl: 42.56
  }
];

// VTable 列配置
const TABLE_COLUMNS = [
  {
    field: 'filename',
    title: '文件名',
    width: 200,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f2f5' },
    style: { textAlign: 'left' }
  },
  {
    field: 'successful_requests',
    title: '成功请求数',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#e6f7ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'benchmark_duration',
    title: '基准时长(s)',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_input_tokens',
    title: '输入Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_generated_tokens',
    title: '生成Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'request_throughput',
    title: '请求吞吐量(req/s)',
    width: 150,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'output_token_throughput',
    title: '输出Token吞吐量(tok/s)',
    width: 180,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'total_token_throughput',
    title: '总Token吞吐量(tok/s)',
    width: 170,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'mean_ttft',
    title: '平均TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'median_ttft',
    title: '中位TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'p99_ttft',
    title: 'P99 TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'mean_tpot',
    title: '平均TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'median_tpot',
    title: '中位TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'p99_tpot',
    title: 'P99 TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'mean_itl',
    title: '平均ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'median_itl',
    title: '中位ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'p99_itl',
    title: 'P99 ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  }
];

const VTableDemo = () => {
  const [tableInstance, setTableInstance] = useState(null);
  const containerRef = useRef(null);

  // 初始化VTable
  useEffect(() => {
    if (containerRef.current) {
      // 清理现有表格
      if (tableInstance) {
        tableInstance.release();
      }

      const option = {
        container: containerRef.current,
        columns: TABLE_COLUMNS,
        records: DEMO_DATA,
        theme: VTable.themes.DEFAULT.extends({
          headerStyle: {
            borderColor: '#e8e8e8',
            borderLineWidth: 1
          },
          bodyStyle: {
            borderColor: '#f0f0f0',
            borderLineWidth: 1
          }
        }),
        widthMode: 'adaptive',
        heightMode: 'adaptive',
        autoWrapText: true,
        hover: {
          highlightMode: 'row'
        },
        select: {
          highlightMode: 'row'
        },
        menu: {
          contextMenuItems: ['复制', '导出选中行', '导出全部数据']
        }
      };

      const table = new VTable.ListTable(option);
      setTableInstance(table);

      // 监听菜单点击事件
      table.on('dropdown_menu_click', (args) => {
        console.log('菜单点击:', args);
        if (args.menuKey === '导出全部数据') {
          exportData();
        }
      });

      return () => {
        if (table) {
          table.release();
        }
      };
    }
  }, []);

  // 导出数据
  const exportData = () => {
    const csvContent = [
      // CSV 头部
      TABLE_COLUMNS.map(col => col.title).join(','),
      // CSV 数据行
      ...DEMO_DATA.map(row => 
        TABLE_COLUMNS.map(col => row[col.field] || '').join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `vtable_demo_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('数据导出成功');
  };

  const refreshData = () => {
    message.info('这是演示数据，实际使用时会重新加载真实数据');
  };

  return (
    <Card 
      title={
        <Space>
          <BarChartOutlined />
          <span>VTable 高性能日志分析表格演示</span>
          <Tag color="blue">{DEMO_DATA.length} 条记录</Tag>
        </Space>
      }
      extra={
        <Space>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={refreshData}
          >
            刷新数据
          </Button>
          <Button 
            icon={<DownloadOutlined />}
            onClick={exportData}
          >
            导出数据
          </Button>
        </Space>
      }
      style={{ margin: '20px' }}
    >
      <div 
        ref={containerRef} 
        style={{ 
          height: '500px', 
          width: '100%',
          border: '1px solid #d9d9d9',
          borderRadius: '6px'
        }} 
      />
      
      <div style={{ marginTop: '16px', color: '#666' }}>
        <p><strong>功能特点：</strong></p>
        <ul>
          <li>🚀 <strong>高性能渲染</strong>：基于字节跳动开源的 VTable，支持大数据量表格渲染</li>
          <li>🎨 <strong>丰富样式</strong>：支持列头背景色、字体颜色、对齐方式等自定义样式</li>
          <li>📊 <strong>智能排序</strong>：点击列头可进行升序/降序排序</li>
          <li>🖱️ <strong>交互体验</strong>：支持行悬停高亮、行选择等交互效果</li>
          <li>📋 <strong>右键菜单</strong>：支持复制、导出等操作</li>
          <li>📈 <strong>数据导出</strong>：支持导出为 CSV 格式</li>
          <li>🔍 <strong>智能提取</strong>：基于正则表达式模板自动提取日志中的性能指标</li>
        </ul>
      </div>
    </Card>
  );
};

export default VTableDemo;
