# 性能测试数据表格调试指南

## 🔍 问题分析

### 原始问题
用户点击编辑按钮时，浏览器网络面板显示调用了大量API接口，这表明：
1. 每次状态变化都触发了数据重新获取
2. useEffect的依赖项可能导致了不必要的重新渲染
3. 事件处理函数没有正确优化

### 修复措施

#### 1. 添加调试日志
```javascript
console.log('fetchModelData called for model:', modelName);
console.log('Edit clicked for record:', record.key);
console.log('Save called for key:', key);
```

#### 2. 使用useCallback优化函数
```javascript
const edit = useCallback((record) => {
  // 编辑逻辑
}, [editingKey, editForm]);

const save = useCallback(async (key) => {
  // 保存逻辑
}, [editForm, modelData]);

const fetchModelData = useCallback(async () => {
  // 数据获取逻辑
}, [modelName, editingKey]);
```

#### 3. 防止编辑时重新获取数据
```javascript
// 防止在编辑状态下重新获取数据
if (editingKey !== '') {
  console.log('Skipping fetchModelData because editing is in progress');
  return;
}
```

## 🧪 测试步骤

### 1. 打开浏览器开发者工具
- 按F12打开开发者工具
- 切换到Network（网络）面板
- 清空现有请求记录

### 2. 测试编辑功能
1. **点击编辑按钮**
   - 观察Network面板，应该只有最初的数据获取请求
   - 不应该有额外的API调用
   - 控制台应该显示："Edit clicked for record: [key]"

2. **双击单元格编辑**
   - 应该直接进入编辑模式
   - 不应该触发API调用

3. **保存数据**
   - 按回车或点击其他地方保存
   - 应该只有一个保存API调用
   - 控制台应该显示："Save called for key: [key]"

### 3. 测试数据隔离
1. **切换不同模型**
   - 在URL中更改模型名称
   - 应该只调用一次数据获取API
   - 控制台应该显示："fetchModelData called for model: [modelName]"

2. **验证数据过滤**
   - 确认不同模型显示不同的数据
   - 新增数据应该关联到当前模型

## 🔧 预期行为

### 正常情况下的API调用
1. **页面加载**：1次GET请求获取模型数据
2. **点击编辑**：0次API调用（仅状态变化）
3. **保存数据**：1次POST/PUT请求保存数据
4. **删除数据**：1次DELETE请求删除数据
5. **切换模型**：1次GET请求获取新模型数据

### 异常情况（需要修复）
- 点击编辑时出现多次GET请求
- 状态变化时不必要的数据重新获取
- 编辑过程中的意外API调用

## 📊 性能监控

### 控制台日志监控
打开控制台，查看以下日志：
```
fetchModelData called for model: [modelName]  // 应该只在必要时出现
Edit clicked for record: [key]                // 点击编辑时出现
Save called for key: [key]                    // 保存时出现
Skipping fetchModelData because editing is in progress  // 编辑时跳过数据获取
```

### 网络请求监控
在Network面板中监控：
- `GET /api/model-storage/performance-test-data/` - 数据获取
- `POST /api/model-storage/performance-test-data/` - 创建数据
- `PUT /api/model-storage/performance-test-data/[id]/` - 更新数据
- `DELETE /api/model-storage/performance-test-data/[id]/` - 删除数据

## 🚨 故障排除

### 如果仍然有多余的API调用
1. 检查控制台日志，确认调用来源
2. 检查是否有其他组件也在监听相同的状态变化
3. 确认useEffect的依赖项是否正确
4. 检查是否有循环依赖导致的重新渲染

### 如果编辑功能异常
1. 确认editingKey状态管理正确
2. 检查表单验证规则
3. 确认事件处理函数绑定正确
4. 检查数据结构是否一致

## 📝 后续优化建议

1. **添加请求缓存**：避免重复请求相同数据
2. **实现乐观更新**：先更新UI，再同步到服务器
3. **添加加载状态**：改善用户体验
4. **错误重试机制**：网络错误时自动重试
5. **数据分页**：大量数据时的性能优化
