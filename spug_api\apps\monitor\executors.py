# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django_redis import get_redis_connection
from apps.host.models import Host
from apps.monitor.utils import handle_notify
from socket import socket
import subprocess
import platform
import requests
import logging
import json
import time
import re

logging.captureWarnings(True)
regex = re.compile(r'Failed to establish a new connection: (.*)\'\)+')


def site_check(url, limit):
    try:
        res = requests.get(url, timeout=30)
        if limit:
            duration = int(res.elapsed.total_seconds() * 1000)
            if duration > int(limit):
                return False, f'响应时间 {duration}ms 大于 {limit}ms'
        return 200 <= res.status_code < 400, f'返回HTTP状态码 {res.status_code}'
    except Exception as e:
        error = e.__str__()
        exps = re.findall(regex, error)
        if exps:
            error = exps[0]
        return False, error


def port_check(addr, port):
    try:
        sock = socket()
        sock.settimeout(5)
        sock.connect((addr, int(port)))
        sock.close()
        return True, '端口状态检测正常'
    except Exception as e:
        return False, f'异常信息：{e}'


def ping_check(addr):
    try:
        if platform.system().lower() == 'windows':
            command = f'ping -n 1 -w 3000 {addr}'
        else:
            command = f'ping -c 1 -W 3 {addr}'
        task = subprocess.run(command, shell=True, stdout=subprocess.PIPE)
        if task.returncode == 0:
            return True, 'Ping检测正常'
        else:
            return False, 'Ping检测失败'
    except Exception as e:
        return False, f'异常信息：{e}'


def host_executor(host, command):
    try:
        with host.get_ssh() as ssh:
            exit_code, out = ssh.exec_command_raw(command)
        if exit_code == 0:
            return True, out or '检测状态正常'
        else:
            return False, out or f'退出状态码：{exit_code}'
    except Exception as e:
        return False, f'异常信息：{e}'


def get_running_containers(host):
    """获取主机上运行中的容器列表"""
    try:
        with host.get_ssh() as ssh:
            # 获取运行中的容器名称
            exit_code, out = ssh.exec_command_raw("docker ps --format '{{.Names}}' 2>/dev/null")
            if exit_code == 0 and out.strip():
                return [name.strip() for name in out.strip().split('\n') if name.strip()]
            return []
    except Exception:
        return []


def search_process_in_container(host, container_name, process_name):
    """在指定容器中搜索进程"""
    try:
        with host.get_ssh() as ssh:
            # 在容器中搜索进程，获取详细信息包括运行时间
            command = f"docker exec {container_name} ps -eo pid,ppid,cmd,etime,pcpu,pmem --no-headers 2>/dev/null | grep -v grep | grep '{process_name}'"
            exit_code, out = ssh.exec_command_raw(command)

            if exit_code == 0 and out.strip():
                processes = []
                for line in out.strip().split('\n'):
                    if line.strip():
                        # 使用正则表达式解析ps输出，更准确地分割字段
                        import re
                        # ps输出格式: PID PPID CMD ETIME PCPU PMEM
                        # 使用正则匹配：数字 数字 命令(可能包含空格) 时间格式 数字 数字
                        pattern = r'^\s*(\d+)\s+(\d+)\s+(.+?)\s+(\d+:\d+:\d+|\d+-\d+:\d+:\d+)\s+([\d.]+)\s+([\d.]+)\s*$'
                        match = re.match(pattern, line)

                        if match:
                            processes.append({
                                'pid': match.group(1),
                                'ppid': match.group(2),
                                'cmd': match.group(3).strip(),
                                'etime': match.group(4),  # 运行时间
                                'pcpu': match.group(5),   # CPU使用率
                                'pmem': match.group(6),   # 内存使用率
                                'location': f'容器:{container_name}'
                            })
                        else:
                            # 如果正则匹配失败，使用原来的分割方法作为备选
                            parts = line.strip().split()
                            if len(parts) >= 6:
                                # 重新组合命令部分（从第3个到倒数第4个）
                                cmd_parts = parts[2:-3]
                                processes.append({
                                    'pid': parts[0],
                                    'ppid': parts[1],
                                    'cmd': ' '.join(cmd_parts),
                                    'etime': parts[-3],  # 运行时间
                                    'pcpu': parts[-2],   # CPU使用率
                                    'pmem': parts[-1],   # 内存使用率
                                    'location': f'容器:{container_name}'
                                })
                return True, processes
            return False, []
    except Exception as e:
        return False, f'容器 {container_name} 进程查询异常：{e}'


def search_process_on_host(host, process_name):
    """在主机上搜索进程"""
    try:
        with host.get_ssh() as ssh:
            # 在主机上搜索进程，获取详细信息包括运行时间
            command = f"ps -eo pid,ppid,cmd,etime,pcpu,pmem --no-headers | grep -v grep | grep '{process_name}'"
            exit_code, out = ssh.exec_command_raw(command)

            if exit_code == 0 and out.strip():
                processes = []
                for line in out.strip().split('\n'):
                    if line.strip():
                        # 使用正则表达式解析ps输出，更准确地分割字段
                        import re
                        # ps输出格式: PID PPID CMD ETIME PCPU PMEM
                        # 使用正则匹配：数字 数字 命令(可能包含空格) 时间格式 数字 数字
                        pattern = r'^\s*(\d+)\s+(\d+)\s+(.+?)\s+(\d+:\d+:\d+|\d+-\d+:\d+:\d+)\s+([\d.]+)\s+([\d.]+)\s*$'
                        match = re.match(pattern, line)

                        if match:
                            processes.append({
                                'pid': match.group(1),
                                'ppid': match.group(2),
                                'cmd': match.group(3).strip(),
                                'etime': match.group(4),  # 运行时间
                                'pcpu': match.group(5),   # CPU使用率
                                'pmem': match.group(6),   # 内存使用率
                                'location': f'主机:{host.name}'
                            })
                        else:
                            # 如果正则匹配失败，使用原来的分割方法作为备选
                            parts = line.strip().split()
                            if len(parts) >= 6:
                                # 重新组合命令部分（从第3个到倒数第4个）
                                cmd_parts = parts[2:-3]
                                processes.append({
                                    'pid': parts[0],
                                    'ppid': parts[1],
                                    'cmd': ' '.join(cmd_parts),
                                    'etime': parts[-3],  # 运行时间
                                    'pcpu': parts[-2],   # CPU使用率
                                    'pmem': parts[-1],   # 内存使用率
                                    'location': f'主机:{host.name}'
                                })
                return True, processes
            return False, []
    except Exception as e:
        return False, f'主机进程查询异常：{e}'


def extract_search_keywords(process_name):
    """从进程名中提取搜索关键词"""
    import re

    # 移除常见的文件扩展名
    name = re.sub(r'\.(sh|py|pl|rb|js|jar|exe)$', '', process_name)

    # 提取可能的关键词
    keywords = []

    # 1. 完整名称
    keywords.append(process_name)
    keywords.append(name)

    # 2. 提取数字和字母组合的关键词 (如: 28k, 4k, qwen2)
    parts = re.findall(r'[a-zA-Z]+\d*|\d+[a-zA-Z]*', name)
    keywords.extend(parts)

    # 3. 提取纯字母关键词 (如: qwen, run)
    alpha_parts = re.findall(r'[a-zA-Z]{3,}', name)  # 至少3个字母
    keywords.extend(alpha_parts)

    # 4. 移除重复并按长度排序（长的优先）
    unique_keywords = list(set(keywords))
    unique_keywords.sort(key=len, reverse=True)

    return unique_keywords


def smart_process_check(host, process_name, search_mode='container_first'):
    """智能进程检测：先容器后主机，支持模糊匹配"""
    all_processes = []
    search_results = []

    # 提取搜索关键词
    search_keywords = extract_search_keywords(process_name)
    search_results.append(f'提取的搜索关键词: {", ".join(search_keywords[:5])}')  # 只显示前5个

    try:
        if search_mode in ['container_first', 'container_only']:
            # 1. 先在容器中搜索
            containers = get_running_containers(host)
            search_results.append(f'发现 {len(containers)} 个运行中的容器')

            # 尝试每个关键词，直到找到进程
            for keyword in search_keywords:
                if all_processes:  # 如果已经找到进程，跳出循环
                    break

                for container in containers:
                    is_found, result = search_process_in_container(host, container, keyword)
                    if is_found and result:
                        all_processes.extend(result)
                        search_results.append(f'容器 {container} 中使用关键词 "{keyword}" 找到 {len(result)} 个进程')

        # 2. 如果容器中没找到且允许搜索主机，则在主机上搜索
        if not all_processes and search_mode in ['container_first', 'host_only']:
            # 尝试每个关键词
            for keyword in search_keywords:
                if all_processes:  # 如果已经找到进程，跳出循环
                    break

                is_found, result = search_process_on_host(host, keyword)
                if is_found and result:
                    all_processes.extend(result)
                    search_results.append(f'主机上使用关键词 "{keyword}" 找到 {len(result)} 个进程')

            if not all_processes:
                search_results.append('主机上未找到匹配进程')

        if all_processes:
            # 格式化输出结果
            result_lines = [f'找到 {len(all_processes)} 个匹配的进程:']
            result_lines.extend(search_results)
            result_lines.append('')

            for i, proc in enumerate(all_processes, 1):
                result_lines.append(f'进程 {i}:')
                result_lines.append(f'  PID: {proc["pid"]}')
                result_lines.append(f'  命令: {proc["cmd"]}')
                result_lines.append(f'  运行时间: {proc["etime"]}')
                result_lines.append(f'  CPU使用率: {proc["pcpu"]}%')
                result_lines.append(f'  内存使用率: {proc["pmem"]}%')
                result_lines.append(f'  位置: {proc["location"]}')
                result_lines.append('')

            return True, '\n'.join(result_lines)
        else:
            result_lines = [f'未找到进程 "{process_name}"']
            result_lines.extend(search_results)
            return False, '\n'.join(result_lines)

    except Exception as e:
        return False, f'智能进程检测异常：{e}'


def monitor_worker_handler(job):
    task_id, tp, addr, extra, threshold, quiet = json.loads(job)
    target = addr
    if tp == '1':
        is_ok, message = site_check(addr, extra)
    elif tp == '2':
        is_ok, message = port_check(addr, extra)
    elif tp == '5':
        is_ok, message = ping_check(addr)
    elif tp == '6':
        # 智能进程检测
        host = Host.objects.filter(pk=addr).first()
        if not host:
            is_ok, message = False, f'unknown host id for {addr!r}'
        else:
            # extra 可能包含进程名和搜索模式，格式：进程名|搜索模式
            if '|' in extra:
                process_name, search_mode = extra.split('|', 1)
            else:
                process_name, search_mode = extra, 'container_first'
            is_ok, message = smart_process_check(host, process_name.strip(), search_mode.strip())
        target = f'{host.name}({host.hostname})'
    elif tp not in ('3', '4'):
        is_ok, message = False, f'invalid monitor type for {tp!r}'
    else:
        command = f'ps -ef|grep -v grep|grep {extra!r}' if tp == '3' else extra
        host = Host.objects.filter(pk=addr).first()
        if not host:
            is_ok, message = False, f'unknown host id for {addr!r}'
        else:
            is_ok, message = host_executor(host, command)
        target = f'{host.name}({host.hostname})'

    rds, key, f_count, f_time = get_redis_connection(), f'spug:det:{task_id}', f'c_{addr}', f't_{addr}'
    v_count, v_time = rds.hmget(key, f_count, f_time)

    # 存储进程详细信息（仅对智能进程检测）
    if tp == '6' and is_ok:
        # 解析进程信息并存储到Redis
        process_info_key = f'spug:det:info:{task_id}_{addr}'
        try:
            # 从message中提取进程信息
            if '找到' in message and '个匹配的进程' in message:
                lines = message.split('\n')
                process_details = []
                current_process = {}

                for line in lines:
                    line = line.strip()
                    if line.startswith('进程 '):
                        if current_process:
                            process_details.append(current_process)
                        current_process = {}
                    elif line.startswith('PID: '):
                        current_process['pid'] = line.replace('PID: ', '')
                    elif line.startswith('命令: '):
                        current_process['cmd'] = line.replace('命令: ', '')
                    elif line.startswith('运行时间: '):
                        current_process['etime'] = line.replace('运行时间: ', '')
                    elif line.startswith('CPU使用率: '):
                        current_process['pcpu'] = line.replace('CPU使用率: ', '').replace('%', '')
                    elif line.startswith('内存使用率: '):
                        current_process['pmem'] = line.replace('内存使用率: ', '').replace('%', '')
                    elif line.startswith('位置: '):
                        current_process['location'] = line.replace('位置: ', '')

                if current_process:
                    process_details.append(current_process)

                # 存储进程详细信息到Redis，设置过期时间为1小时
                if process_details:
                    rds.setex(process_info_key, 3600, json.dumps(process_details))
                    logging.info(f'Stored process info for {process_info_key}: {len(process_details)} processes')
        except Exception as e:
            logging.warning(f'Failed to store process info: {e}')

    if is_ok:
        if v_count:
            rds.hdel(key, f_count, f_time)
        # 移除恢复通知和正常状态通知，只在故障时发送邮件
        return
    v_count = rds.hincrby(key, f_count)
    if v_count >= threshold:
        if not v_time or int(time.time()) - int(v_time) >= quiet * 60:
            rds.hset(key, f_time, int(time.time()))
            logging.warning('send fault alarm notification')
            handle_notify(task_id, target, is_ok, message, v_count)


def dispatch(tp, addr, extra):
    if tp == '1':
        return site_check(addr, extra)
    elif tp == '2':
        return port_check(addr, extra)
    elif tp == '5':
        return ping_check(addr)
    elif tp == '6':
        # 智能进程检测
        host = Host.objects.filter(pk=addr).first()
        if not host:
            return False, f'unknown host id for {addr!r}'
        # extra 可能包含进程名和搜索模式，格式：进程名|搜索模式
        if '|' in extra:
            process_name, search_mode = extra.split('|', 1)
        else:
            process_name, search_mode = extra, 'container_first'
        return smart_process_check(host, process_name.strip(), search_mode.strip())
    elif tp == '3':
        command = f'ps -ef|grep -v grep|grep {extra!r}'
    elif tp == '4':
        command = extra
    else:
        raise TypeError(f'invalid monitor type: {tp!r}')
    host = Host.objects.filter(pk=addr).first()
    return host_executor(host, command)
