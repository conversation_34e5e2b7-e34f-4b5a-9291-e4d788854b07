.modelDataTable {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.modelDataTable .ant-card {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modelDataTable .ant-card-head {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 12px 12px 0 0;
}

.modelDataTable .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.modelDataTable .ant-card-body {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 12px 12px;
  padding: 24px;
}

/* 表格容器样式 */
.modelDataTable .ant-table-wrapper {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.modelDataTable .ant-table {
  border-radius: 8px;
}

.modelDataTable .ant-table-thead > tr > th {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  font-weight: 600;
  color: #1f2937;
}

.modelDataTable .ant-table-tbody > tr:hover > td {
  background: rgba(24, 144, 255, 0.05);
}

.modelDataTable .editable-row .ant-form-item {
  margin-bottom: 0;
}

/* 编辑状态指示器 */
.hasChanges {
  position: relative;
}

.hasChanges::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* Excel风格紧凑行高 */
.modelDataTable .ant-table-small .ant-table-thead > tr > th {
  padding: 6px 8px;
  height: 32px;
  font-size: 12px;
  font-weight: 600;
}

.modelDataTable .ant-table-small .ant-table-tbody > tr > td {
  padding: 4px 8px;
  height: 28px;
  font-size: 12px;
  line-height: 20px;
}

/* 原生Excel编辑体验 */
.modelDataTable .ant-table-tbody > tr > td {
  transition: all 0.2s ease;
}

.modelDataTable .ant-table-tbody > tr > td:hover {
  background-color: #f5f5f5 !important;
}

/* 编辑状态样式 */
.modelDataTable .editable-row .ant-form-item {
  margin: 0;
}

.modelDataTable .editable-row .ant-form-item-control-input {
  min-height: 20px;
}

.modelDataTable .editable-row .ant-input {
  height: 100% !important;
  padding: 2px 4px !important;
  font-size: 12px !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.modelDataTable .editable-row .ant-input:focus {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  border: 1px solid #1890ff;
  border-radius: 2px;
}

.modelDataTable .editable-row .ant-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 1px rgba(24, 144, 255, 0.2);
}

/* 操作按钮样式 */
.modelDataTable .ant-btn-link {
  height: auto;
  padding: 0 4px;
  font-size: 12px;
  line-height: 20px;
}

/* 斑马纹效果 */
.modelDataTable .ant-table-tbody > tr:nth-child(even) > td {
  background-color: #fafafa;
}

.modelDataTable .ant-table-tbody > tr:nth-child(even):hover > td {
  background-color: rgba(24, 144, 255, 0.05);
}

/* 按钮样式增强 */
.modelDataTable .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.modelDataTable .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.modelDataTable .ant-btn-primary:active {
  transform: translateY(0);
}

/* 保存按钮特殊样式 */
.saveButton {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3) !important;
}

.saveButton:hover {
  background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%) !important;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4) !important;
}

/* 模态框样式 */
.modelDataTable .ant-modal-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.modelDataTable .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.modelDataTable .ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modelDataTable {
    padding: 16px;
  }

  .modelDataTable .ant-table-wrapper {
    overflow-x: auto;
  }

  .modelDataTable .ant-card-head-title {
    font-size: 16px;
  }
}

/* 加载状态样式 */
.modelDataTable .ant-spin-container {
  min-height: 400px;
}

.modelDataTable .ant-spin-spinning {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

/* 表格工具栏样式 */
.tableToolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.tableInfo {
  color: #6b7280;
  font-size: 14px;
}

.tableActions {
  display: flex;
  gap: 8px;
}

/* 状态标签样式 */
.statusTag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.statusTag.pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.statusTag.inProgress {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.statusTag.completed {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.statusTag.cancelled {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
}

/* 可编辑单元格提示 */
.editableHint {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #1890ff;
  opacity: 0.6;
}

/* 编辑状态样式 */
.modelDataTable .ant-table-tbody .editable-row:hover {
  background-color: rgba(24, 144, 255, 0.05);
}

.modelDataTable .ant-form-item-has-error .ant-input,
.modelDataTable .ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
}

.modelDataTable .ant-form-item-explain-error {
  font-size: 12px;
  color: #ff4d4f;
}
