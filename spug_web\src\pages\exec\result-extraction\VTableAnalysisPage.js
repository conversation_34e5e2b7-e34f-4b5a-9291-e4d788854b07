import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, message, Space, Spin, Progress, Tag, Breadcrumb, Modal, Input, Switch, Tooltip, Alert, Badge, Divider, Table } from 'antd';
import { ArrowLeftOutlined, DownloadOutlined, ReloadOutlined, <PERSON><PERSON><PERSON>Outlined, SaveOutlined, RobotOutlined, B<PERSON>bOutlined, ExclamationCircleOutlined, CopyOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import * as VTable from '@visactor/vtable';
import * as XLSX from 'xlsx';
import { http } from 'libs';

// 智能提取模板 - 基于您提供的固定格式，支持多个空格
const LOG_EXTRACTION_TEMPLATE = {
  patterns: [
    { key: 'successful_requests', pattern: /Successful requests:\s+([\d,]+)/, type: 'number' },
    { key: 'benchmark_duration', pattern: /Benchmark duration \(s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_input_tokens', pattern: /Total input tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'total_generated_tokens', pattern: /Total generated tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'request_throughput', pattern: /Request throughput \(req\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'output_token_throughput', pattern: /Output token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_token_throughput', pattern: /Total Token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_ttft', pattern: /Mean TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_ttft', pattern: /Median TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_ttft', pattern: /P99 TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_tpot', pattern: /Mean TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_tpot', pattern: /Median TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_tpot', pattern: /P99 TPOT \(ms\):\s+([\d.]+)/, type: 'number' }
  ]
};

// VTable列配置
const VTABLE_COLUMNS = [
  {
    field: 'filename',
    title: '文件名',
    width: 200,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'left', color: '#1890ff' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['filename'],
      filterMode: 'fuzzyMatch'
    }
  },
  {
    field: 'successful_requests',
    title: '成功请求数',
    width: 120,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['successful_requests'],
      filterMode: 'condition'
    }
  },
  {
    field: 'benchmark_duration',
    title: '基准时长(s)',
    width: 120,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff7e6' },
    style: { textAlign: 'center', color: '#fa8c16' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['benchmark_duration'],
      filterMode: 'condition'
    }
  },
  {
    field: 'total_input_tokens',
    title: '输入Token总数',
    width: 140,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['total_input_tokens'],
      filterMode: 'condition'
    }
  },
  {
    field: 'total_generated_tokens',
    title: '生成Token总数',
    width: 140,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['total_generated_tokens'],
      filterMode: 'condition'
    }
  },
  {
    field: 'request_throughput',
    title: '请求吞吐量(req/s)',
    width: 150,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['request_throughput'],
      filterMode: 'condition'
    }
  },
  {
    field: 'output_token_throughput',
    title: '输出Token吞吐量(tok/s)',
    width: 180,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['output_token_throughput'],
      filterMode: 'condition'
    }
  },
  {
    field: 'total_token_throughput',
    title: '总Token吞吐量(tok/s)',
    width: 170,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['total_token_throughput'],
      filterMode: 'condition'
    }
  },
  {
    field: 'mean_ttft',
    title: '平均TTFT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2f0' },
    style: { textAlign: 'center', color: '#ff4d4f' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['mean_ttft'],
      filterMode: 'condition'
    }
  },
  {
    field: 'median_ttft',
    title: '中位TTFT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2f0' },
    style: { textAlign: 'center', color: '#ff4d4f' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['median_ttft'],
      filterMode: 'condition'
    }
  },
  {
    field: 'p99_ttft',
    title: 'P99 TTFT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2f0' },
    style: { textAlign: 'center', color: '#ff4d4f' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['p99_ttft'],
      filterMode: 'condition'
    }
  },
  {
    field: 'mean_tpot',
    title: '平均TPOT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['mean_tpot'],
      filterMode: 'condition'
    }
  },
  {
    field: 'median_tpot',
    title: '中位TPOT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['median_tpot'],
      filterMode: 'condition'
    }
  },
  {
    field: 'p99_tpot',
    title: 'P99 TPOT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['p99_tpot'],
      filterMode: 'condition'
    }
  }
];

const VTableAnalysisPage = () => {
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [progress, setProgress] = useState(0);
  const [tableInstance, setTableInstance] = useState(null);
  const [analysisParams, setAnalysisParams] = useState(null);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [saveName, setSaveName] = useState('');
  const [saveDescription, setSaveDescription] = useState('');
  const [saving, setSaving] = useState(false);
  const [sortState, setSortState] = useState([]); // 排序状态

  // AI分析相关状态
  const [aiAnalysisResult, setAiAnalysisResult] = useState(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [showAiResult, setShowAiResult] = useState(false);

  const containerRef = useRef(null);
  const history = useHistory();

  // AI分析功能 - 对整张表进行分析
  const performTableAiAnalysis = async () => {
    if (extractedData.length === 0) {
      message.warning('请先提取数据');
      return;
    }

    try {
      setAiLoading(true);
      setShowAiResult(false);

      // 构建整张表的汇总数据用于AI分析
      const tableStats = {
        total_files: extractedData.length,
        avg_successful_requests: extractedData.reduce((sum, item) => sum + (item.successful_requests || 0), 0) / extractedData.length,
        avg_benchmark_duration: extractedData.reduce((sum, item) => sum + (item.benchmark_duration || 0), 0) / extractedData.length,
        avg_request_throughput: extractedData.reduce((sum, item) => sum + (item.request_throughput || 0), 0) / extractedData.length,
        avg_output_token_throughput: extractedData.reduce((sum, item) => sum + (item.output_token_throughput || 0), 0) / extractedData.length,
        avg_mean_ttft: extractedData.reduce((sum, item) => sum + (item.mean_ttft || 0), 0) / extractedData.length,
        avg_p99_ttft: extractedData.reduce((sum, item) => sum + (item.p99_ttft || 0), 0) / extractedData.length,
        max_request_throughput: Math.max(...extractedData.map(item => item.request_throughput || 0)),
        min_request_throughput: Math.min(...extractedData.map(item => item.request_throughput || 0)),
        files: extractedData.map(item => ({
          filename: item.filename,
          successful_requests: item.successful_requests,
          request_throughput: item.request_throughput,
          mean_ttft: item.mean_ttft,
          p99_ttft: item.p99_ttft
        }))
      };

      const logContent = `
基准测试批量结果分析:
总文件数: ${tableStats.total_files}
平均成功请求数: ${tableStats.avg_successful_requests.toFixed(2)}
平均基准时长: ${tableStats.avg_benchmark_duration.toFixed(2)}s
平均请求吞吐量: ${tableStats.avg_request_throughput.toFixed(2)} req/s
平均输出Token吞吐量: ${tableStats.avg_output_token_throughput.toFixed(2)} tok/s
平均TTFT: ${tableStats.avg_mean_ttft.toFixed(2)}ms
平均P99 TTFT: ${tableStats.avg_p99_ttft.toFixed(2)}ms
最高请求吞吐量: ${tableStats.max_request_throughput} req/s
最低请求吞吐量: ${tableStats.min_request_throughput} req/s

详细文件数据:
${tableStats.files.map(file =>
  `${file.filename}: 请求数=${file.successful_requests}, 吞吐量=${file.request_throughput}req/s, TTFT=${file.mean_ttft}ms`
).join('\n')}
      `;

      const response = await http.post('/api/ai/analysis/', {
        content: logContent,
        filename: 'batch_analysis_results.log',
        data_type: 'benchmark_batch_analysis'
      });

      if (response && response.success) {
        setAiAnalysisResult(response.ai_analysis);
        setShowAiResult(true);
        message.success('AI分析完成');
      } else {
        message.error('AI分析失败: ' + (response.error || '未知错误'));
      }
    } catch (error) {
      console.error('AI分析错误:', error);
      message.error('AI分析失败: ' + error.message);
    } finally {
      setAiLoading(false);
    }
  };

  // 动态生成列配置
  const getTableColumns = () => {
    return VTABLE_COLUMNS.slice();
  };

  // 加载测试结果数据
  const loadTestResultData = async (resultId) => {
    setLoading(true);
    try {
      const response = await http.get(`/api/exec/test-results/${resultId}/?token=1`);

      if (response) {
        // 如果有原始日志，优先使用原始日志数据
        if (response.raw_log) {
          try {
            const parsedLog = JSON.parse(response.raw_log);
            if (Array.isArray(parsedLog) && parsedLog.length > 0) {
              setExtractedData(parsedLog);
              message.success('测试结果数据加载成功');
              return;
            }
          } catch (e) {

          }
        }

        // 如果没有原始日志或解析失败，使用指标数据
        if (response.metrics && response.metrics.length > 0) {
          // 创建一个基础的VTable行数据
          const vtableRow = {
            filename: response.plan_name || `测试结果_${resultId}`,
            successful_requests: '',
            benchmark_duration: '',
            total_input_tokens: '',
            total_generated_tokens: '',
            request_throughput: '',
            output_token_throughput: '',
            total_token_throughput: '',
            mean_ttft: '',
            median_ttft: '',
            p99_ttft: '',
            mean_tpot: '',
            median_tpot: '',
            p99_tpot: ''
          };

          // 根据指标名称映射到对应字段
          response.metrics.forEach(metric => {
            const name = metric.name.toLowerCase();
            const value = parseFloat(metric.value) || metric.value;

            if (name.includes('successful requests')) {
              vtableRow.successful_requests = value;
            } else if (name.includes('benchmark duration')) {
              vtableRow.benchmark_duration = value;
            } else if (name.includes('total input tokens')) {
              vtableRow.total_input_tokens = value;
            } else if (name.includes('total generated tokens')) {
              vtableRow.total_generated_tokens = value;
            } else if (name.includes('request throughput')) {
              vtableRow.request_throughput = value;
            } else if (name.includes('output token throughput')) {
              vtableRow.output_token_throughput = value;
            } else if (name.includes('total token throughput')) {
              vtableRow.total_token_throughput = value;
            } else if (name.includes('mean ttft')) {
              vtableRow.mean_ttft = value;
            } else if (name.includes('median ttft')) {
              vtableRow.median_ttft = value;
            } else if (name.includes('p99 ttft')) {
              vtableRow.p99_ttft = value;
            } else if (name.includes('mean tpot')) {
              vtableRow.mean_tpot = value;
            } else if (name.includes('median tpot')) {
              vtableRow.median_tpot = value;
            } else if (name.includes('p99 tpot')) {
              vtableRow.p99_tpot = value;
            }
          });

          setExtractedData([vtableRow]);
          message.success('测试结果数据加载成功');
        } else {
          message.error('测试结果中没有找到指标数据');
        }
      } else {
        message.error('测试结果数据格式错误');
      }
    } catch (error) {
      message.error('加载测试结果失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 处理后端解析的数据
  const processBackendData = useCallback((parsedData, filename) => {
    // 如果后端已经解析了数据，直接使用
    if (parsedData && typeof parsedData === 'object') {
      return Object.assign({ filename: filename }, parsedData);
    }

    // 如果后端没有解析数据，返回默认结构
    const defaultResult = { filename };
    LOG_EXTRACTION_TEMPLATE.patterns.forEach(({ key, type }) => {
      defaultResult[key] = type === 'number' ? 0 : '-';
    });

    return defaultResult;
  }, []);

  // 获取文件内容并提取数据
  const processFiles = useCallback(async () => {
    if (!analysisParams || !analysisParams.selectedFiles) {
      message.error('缺少分析参数，请重新选择文件');
      return;
    }

    const { selectedFiles, hostId, containerName } = analysisParams;
    setLoading(true);
    setProgress(0);
    const results = [];

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setProgress(Math.round(((i + 1) / selectedFiles.length) * 100));

        try {
          // 调用API获取文件内容和解析数据
          const apiUrl = `/api/exec/docker-file-content/?host_id=${hostId}&container_name=${containerName}&file_path=${encodeURIComponent(file.fullPath)}&token=1`;
          const response = await fetch(apiUrl);

          if (response.ok) {
            const responseData = await response.json();

            // 检查spug的标准响应格式: {data: {...}, error: ''}
            if (responseData.error) {
              const errorData = Object.assign({ filename: file.name }, Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '错误'])));
              results.push(errorData);
            } else if (responseData.data) {
              const data = responseData.data;

              if (data.success) {
                const processedData = processBackendData(data.parsed_data, file.name);
                results.push(processedData);
              } else {
                const emptyData = Object.assign({ filename: file.name }, Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '-'])));
                results.push(emptyData);
              }
            } else {
              const formatErrorData = Object.assign({ filename: file.name }, Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '格式错误'])));
              results.push(formatErrorData);
            }
          } else {
            const httpErrorData = Object.assign({ filename: file.name }, Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : 'HTTP错误'])));
            results.push(httpErrorData);
          }
        } catch (error) {
          const exceptionData = Object.assign({ filename: file.name }, Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '请求异常'])));
          results.push(exceptionData);
        }

        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setExtractedData(results);

      if (results.length > 0) {
        message.success(`成功分析 ${results.length} 个日志文件`);
      } else {
        message.warning('未能提取到有效数据');
      }
    } catch (error) {
      message.error('分析过程中发生错误: ' + error.message);
    } finally {
      setLoading(false);
      setProgress(0);
    }
  }, [analysisParams, processBackendData]);

  // 导出数据到Excel
  const exportData = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      let dataToExport = extractedData;

      // 如果表格实例存在，获取当前排序后的数据
      if (window.vtableInstance) {
        const table = window.vtableInstance;

        try {
          // 尝试多种方法获取当前排序后的数据
          if (table.records && Array.isArray(table.records)) {
            dataToExport = table.records;
          } else if (typeof table.getRecords === 'function') {
            dataToExport = table.getRecords();
          } else if (typeof table.getAllRowsData === 'function') {
            dataToExport = table.getAllRowsData();
          } else {
            // 使用原始数据，但根据当前排序状态进行排序
            dataToExport = [...extractedData];
            if (sortState && sortState.length > 0) {
              dataToExport.sort((a, b) => {
                for (const sort of sortState) {
                  const { field, order } = sort;
                  const aVal = a[field];
                  const bVal = b[field];

                  let comparison = 0;
                  if (aVal < bVal) comparison = -1;
                  else if (aVal > bVal) comparison = 1;

                  if (comparison !== 0) {
                    return order === 'desc' ? -comparison : comparison;
                  }
                }
                return 0;
              });
            }
          }
        } catch (error) {
          console.error('获取表格数据失败:', error);
          dataToExport = extractedData;
        }
      }

      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '基准测试结果');

      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `基准测试分析结果_${timestamp}.xlsx`;

      // 使用浏览器原生下载
      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('数据导出成功');
    } catch (error) {

      message.error('导出失败: ' + error.message);
    }
  };

  // 保存结果记录
  const handleSave = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可保存');
      return;
    }

    // 生成默认名称
    const timestamp = new Date().toLocaleString('zh-CN');
    const defaultName = `基准测试分析结果_${timestamp}`;
    setSaveName(defaultName);
    setSaveDescription(`包含 ${extractedData.length} 个文件的基准测试分析结果`);
    setSaveModalVisible(true);
  };

  // 确认保存
  const confirmSave = async () => {
    if (!saveName.trim()) {
      message.error('请输入结果名称');
      return;
    }

    setSaving(true);
    try {
      let dataToSave = extractedData;

      // 如果表格实例存在，获取当前排序后的数据
      if (window.vtableInstance) {
        const table = window.vtableInstance;

        try {
          // 尝试多种方法获取当前排序后的数据
          if (table.records && Array.isArray(table.records)) {
            dataToSave = table.records;
          } else if (typeof table.getRecords === 'function') {
            dataToSave = table.getRecords();
          } else if (typeof table.getAllRowsData === 'function') {
            dataToSave = table.getAllRowsData();
          } else {
            // 使用原始数据，但根据当前排序状态进行排序
            dataToSave = [...extractedData];
            if (sortState && sortState.length > 0) {
              dataToSave.sort((a, b) => {
                for (const sort of sortState) {
                  const { field, order } = sort;
                  const aVal = a[field];
                  const bVal = b[field];

                  let comparison = 0;
                  if (aVal < bVal) comparison = -1;
                  else if (aVal > bVal) comparison = 1;

                  if (comparison !== 0) {
                    return order === 'desc' ? -comparison : comparison;
                  }
                }
                return 0;
              });
            }
          }
        } catch (error) {
          console.error('获取表格数据失败:', error);
          dataToSave = extractedData;
        }
      }

      const saveData = {
        plan_name: saveName.trim(), // 使用name作为plan_name
        gpu_name: saveDescription.trim() || '',
        source_type: 'log_extraction',
        metrics: dataToSave.map(item => ({
          label: `${item.文件名} - ${item.模型名称}`,
          value: item.推理吞吐量,
          unit: item.单位 || 'req/s',
          confidence: 1.0,
          category: 'benchmark',
          file_name: item.文件名,
          model_name: item.模型名称,
          batch_size: item.批次大小,
          sequence_length: item.序列长度,
          throughput: item.推理吞吐量,
          latency: item.延迟
        })),
        raw_log: JSON.stringify(dataToSave, null, 2),
        total_metrics: dataToSave.length,
        confirmed_metrics: dataToSave.length,
        ai_confidence: 1.0,
        source: 'vtable_analysis',
        log_source_host: analysisParams && analysisParams.hostId,
        log_source_path: analysisParams && analysisParams.containerName,
        log_extraction_method: 'vtable_analysis'
      };

      await http.post('/api/exec/test-results/', saveData);
      message.success('结果保存成功');
      setSaveModalVisible(false);
      setSaveName('');
      setSaveDescription('');
    } catch (error) {

      const errorMsg = (error.response && error.response.data && (error.response.data.error || error.response.data.message)) || error.message || '未知错误';
      message.error('保存失败: ' + errorMsg);
    } finally {
      setSaving(false);
    }
  };

  // 初始化VTable
  useEffect(() => {
    // 清理之前的表格实例
    if (tableInstance) {
      try {
        tableInstance.release();
      } catch (e) {

      }
      setTableInstance(null);
    }

    if (extractedData.length > 0) {
      // 使用setTimeout确保DOM已经渲染
      const timer = setTimeout(() => {
        if (containerRef.current && !tableInstance) {
          try {
            const option = {
              records: extractedData,
              columns: getTableColumns(),
              widthMode: 'standard',
              heightMode: 'autoHeight',
              autoWrapText: true,
              multipleSort: true, // 启用多列排序
              sortState: sortState, // 设置初始排序状态
              theme: VTable.themes.DEFAULT.extends({
                headerStyle: {
                  bgColor: '#fafafa',
                  color: '#000',
                  fontWeight: 'bold'
                },
                bodyStyle: {
                  bgColor: '#fff',
                  color: '#000'
                }
              }),
              // 启用选择功能
              select: {
                highlightMode: 'cell',
                disableSelect: false,
                disableHeaderSelect: false,
                // 启用多选
                enableMultiSelect: true,
                // 选择模式
                selectMode: 'cell'
              },
              // 启用VTable官方的完整键盘快捷键功能
              keyboardOptions: {
                // 复制粘贴功能
                copySelected: true,              // 启用Ctrl+C复制选中内容
                pasteValueToCell: false,         // 禁用粘贴功能（只需要复制）

                // 全选功能
                selectAllOnCtrlA: true,          // 启用Ctrl+A全选

                // 焦点移动功能
                moveFocusCellOnTab: true,        // Tab键移动焦点到下一个单元格
                moveFocusCellOnEnter: true,      // Enter键移动焦点到下一个单元格
                moveEditCellOnArrowKeys: false,  // 方向键移动编辑单元格（禁用，因为我们主要用于查看）

                // 编辑功能
                editCellOnEnter: false,          // Enter键进入编辑状态（禁用，因为我们主要用于查看）

                // 多选功能
                ctrlMultiSelect: true            // 启用Ctrl+鼠标多选功能
              }
            };

            const table = new VTable.ListTable(containerRef.current, option);

            // 存储表格实例到全局变量
            window.vtableInstance = table;

            // 监听全局键盘事件来检测复制操作
            const handleGlobalKeyDown = (event) => {
              // 检查是否在VTable容器内且按下了Ctrl+C
              if (event.ctrlKey && event.key === 'c') {
                const activeElement = document.activeElement;
                const vtableContainer = containerRef.current;
                if (vtableContainer && (vtableContainer.contains(activeElement) || vtableContainer === activeElement)) {
                  // 延迟显示提示，确保复制操作完成
                  setTimeout(() => {
                    message.success('✅ 复制成功！数据已复制到剪贴板');
                  }, 100);
                }
              }
            };

            // 添加全局键盘事件监听
            document.addEventListener('keydown', handleGlobalKeyDown);

            // 确保VTable容器可以获得焦点
            if (containerRef.current) {
              containerRef.current.setAttribute('tabindex', '0');
              containerRef.current.focus();
            }

            // 监听排序事件 - 更新本地状态
            table.on('sort', (args) => {
              const { sortState: newSortState } = args;
              setSortState(newSortState || []);
            });

            // VTable内置复制功能已启用，无需额外代码

            // 保存表格实例到window对象
            window.vtableInstance = table;


            // 添加右键菜单支持
            table.on('cell-contextmenu', (args) => {
              const { col, row, event } = args;
              event.preventDefault();
              
              // 创建自定义右键菜单
              const menuDiv = document.createElement('div');
              menuDiv.className = 'vtable-context-menu';
              menuDiv.style.position = 'absolute';
              menuDiv.style.left = `${event.clientX}px`;
              menuDiv.style.top = `${event.clientY}px`;
              menuDiv.style.backgroundColor = '#fff';
              menuDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
              menuDiv.style.borderRadius = '4px';
              menuDiv.style.padding = '5px 0';
              menuDiv.style.zIndex = '1000';
              
              // 复制单元格内容
              const copyItem = document.createElement('div');
              copyItem.innerText = '复制单元格内容';
              copyItem.style.padding = '8px 12px';
              copyItem.style.cursor = 'pointer';
              copyItem.style.hover = 'backgroundColor: #f5f5f5';
              copyItem.addEventListener('mouseover', () => {
                copyItem.style.backgroundColor = '#f5f5f5';
              });
              copyItem.addEventListener('mouseout', () => {
                copyItem.style.backgroundColor = '#fff';
              });
              copyItem.addEventListener('click', () => {
                const cellValue = table.getCellValue(col, row);
                navigator.clipboard.writeText(cellValue).then(() => {
                  message.success('已复制到剪贴板');
                  document.body.removeChild(menuDiv);
                }).catch(err => {
                  message.error('复制失败: ' + err);
                  document.body.removeChild(menuDiv);
                });
              });
              menuDiv.appendChild(copyItem);
              
              // 复制整行
              const copyRowItem = document.createElement('div');
              copyRowItem.innerText = '复制整行数据';
              copyRowItem.style.padding = '8px 12px';
              copyRowItem.style.cursor = 'pointer';
              copyRowItem.addEventListener('mouseover', () => {
                copyRowItem.style.backgroundColor = '#f5f5f5';
              });
              copyRowItem.addEventListener('mouseout', () => {
                copyRowItem.style.backgroundColor = '#fff';
              });
              copyRowItem.addEventListener('click', () => {
                const rowData = table.getRowData(row);
                const rowText = Object.values(rowData).join('\t');
                navigator.clipboard.writeText(rowText).then(() => {
                  message.success('已复制整行数据到剪贴板');
                  document.body.removeChild(menuDiv);
                }).catch(err => {
                  message.error('复制失败: ' + err);
                  document.body.removeChild(menuDiv);
                });
              });
              menuDiv.appendChild(copyRowItem);
              
              document.body.appendChild(menuDiv);
              
              // 点击其他地方关闭菜单
              const closeMenu = () => {
                if (document.body.contains(menuDiv)) {
                  document.body.removeChild(menuDiv);
                }
                document.removeEventListener('click', closeMenu);
              };
              setTimeout(() => {
                document.addEventListener('click', closeMenu);
              }, 0);
            });
            
            setTableInstance(table);

            // 返回清理函数
            return () => {
              // 移除全局键盘事件监听
              document.removeEventListener('keydown', handleGlobalKeyDown);
            };

          } catch (error) {
            // VTable初始化失败
          }
        }
      }, 200);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [extractedData]);

  // 页面加载时获取分析参数或测试结果ID
  useEffect(() => {
    // 检查URL参数中是否有result_id
    const urlParams = new URLSearchParams(window.location.search);
    const resultId = urlParams.get('result_id');

    if (resultId) {
      // 如果有result_id，加载测试结果数据
      loadTestResultData(resultId);
    } else {
      // 否则从sessionStorage获取分析参数
      const params = sessionStorage.getItem('logAnalysisParams');

      if (params) {
        try {
          const parsedParams = JSON.parse(params);
          setAnalysisParams(parsedParams);
        } catch (error) {
          message.error('参数解析失败，请重新选择文件');
          history.goBack();
        }
      } else {
        message.error('缺少分析参数，请重新选择文件');
        history.goBack();
      }
    }
  }, [history]);

  // 自动开始分析
  useEffect(() => {
    if (analysisParams && !loading && extractedData.length === 0) {
      processFiles();
    }
  }, [analysisParams, loading, extractedData.length, processFiles]);

  return (
    <div style={{ padding: '24px' }}>
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <a onClick={() => history.goBack()}>智能结果收集器</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>VTable数据分析</Breadcrumb.Item>
      </Breadcrumb>

      <Card
        title={
          <Space>
            <BarChartOutlined />
            <span>基准测试数据分析</span>
            {analysisParams && (
              <Tag color="blue">
                {(analysisParams.selectedFiles && analysisParams.selectedFiles.length) || 0} 个文件
              </Tag>
            )}
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="对整张表进行AI智能分析">
              <Button
                type="primary"
                icon={<RobotOutlined />}
                onClick={performTableAiAnalysis}
                loading={aiLoading}
                size="small"
                disabled={extractedData.length === 0}
              >
                AI分析
              </Button>
            </Tooltip>
            {aiAnalysisResult && !showAiResult && (
              <Tooltip title="展开AI分析结果">
                <Button
                  type="default"
                  icon={<RobotOutlined />}
                  onClick={() => setShowAiResult(true)}
                  size="small"
                  style={{ marginLeft: 8 }}
                >
                  查看结果
                </Button>
              </Tooltip>
            )}
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => history.goBack()}
            >
              返回
            </Button>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={processFiles}
              loading={loading}
              disabled={!analysisParams}
            >
              重新分析
            </Button>
            <Button
              icon={<SaveOutlined />}
              onClick={handleSave}
              disabled={extractedData.length === 0}
            >
              保存结果
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={exportData}
              disabled={extractedData.length === 0}
            >
              导出Excel
            </Button>
            <Button
              onClick={() => {
                if (window.vtableInstance && extractedData.length > 0) {
                  try {
                    const table = window.vtableInstance;
                    const headers = VTABLE_COLUMNS.map(col => col.title).join('\t');
                    let currentTableData = [];

                    try {
                      // 尝试多种方法获取当前排序后的数据
                      if (table.records && Array.isArray(table.records)) {
                        currentTableData = table.records;
                      } else if (typeof table.getRecords === 'function') {
                        currentTableData = table.getRecords();
                      } else if (typeof table.getAllRowsData === 'function') {
                        currentTableData = table.getAllRowsData();
                      } else {
                        // 使用原始数据，但根据当前排序状态进行排序
                        currentTableData = [...extractedData];
                        if (sortState && sortState.length > 0) {
                          currentTableData.sort((a, b) => {
                            for (const sort of sortState) {
                              const { field, order } = sort;
                              const aVal = a[field];
                              const bVal = b[field];

                              let comparison = 0;
                              if (aVal < bVal) comparison = -1;
                              else if (aVal > bVal) comparison = 1;

                              if (comparison !== 0) {
                                return order === 'desc' ? -comparison : comparison;
                              }
                            }
                            return 0;
                          });
                        }
                      }
                    } catch (error) {
                      console.error('获取表格数据失败:', error);
                      currentTableData = extractedData;
                    }

                    const rows = currentTableData.map(row =>
                      VTABLE_COLUMNS.map(col => row[col.field] || '').join('\t')
                    ).join('\n');
                    const content = headers + '\n' + rows;

                    navigator.clipboard.writeText(content).then(() => {
                      message.success('已复制表格数据到剪贴板');
                    }).catch(err => {
                      message.error('复制失败: ' + err.message);
                    });
                  } catch (error) {
                    message.error('复制失败，请重试');
                  }
                }
              }}
              disabled={extractedData.length === 0}
            >
              复制表格
            </Button>
          </Space>
        }
      >
        {loading && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Progress percent={progress} status="active" />
              <p>正在分析日志文件... ({progress}%)</p>
            </div>
          </div>
        )}
        
        {!loading && extractedData.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <BarChartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <p>等待分析数据...</p>
          </div>
        )}

        {!loading && extractedData.length > 0 && (
          <div>

            <div
              ref={containerRef}
              style={{
                height: '70vh',
                width: '100%',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                overflow: 'hidden',
                backgroundColor: '#fff',
                cursor: 'default'
              }}
            />
          </div>
        )}
      </Card>

      {/* AI分析结果展示区域 */}
      {showAiResult && aiAnalysisResult && (
        <Card
          title={
            <div>
              <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
              AI智能分析结果
            </div>
          }
          style={{ marginTop: 16 }}
          extra={
            <Button
              size="small"
              onClick={() => setShowAiResult(false)}
            >
              收起
            </Button>
          }
        >
          <div style={{ padding: '16px 0' }}>
            {/* 性能评估 */}
            {aiAnalysisResult.performance_assessment && (
              <div style={{ marginBottom: 24 }}>
                <h4 style={{ color: '#1890ff', marginBottom: 12 }}>
                  <BarChartOutlined style={{ marginRight: 8 }} />
                  性能评估
                </h4>
                <div style={{
                  padding: 16,
                  backgroundColor: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  borderRadius: 6
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <span style={{ fontWeight: 'bold', marginRight: 8 }}>评分：</span>
                    <Badge
                      count={aiAnalysisResult.performance_assessment.score}
                      style={{ backgroundColor: '#52c41a' }}
                    />
                    <span style={{ marginLeft: 8, color: '#52c41a' }}>
                      ({aiAnalysisResult.performance_assessment.level})
                    </span>
                  </div>
                  <div>
                    <span style={{ fontWeight: 'bold', marginRight: 8 }}>总结：</span>
                    {aiAnalysisResult.performance_assessment.summary}
                  </div>
                </div>
              </div>
            )}

            {/* 异常检测 */}
            {aiAnalysisResult.anomalies && aiAnalysisResult.anomalies.length > 0 && (
              <div style={{ marginBottom: 24 }}>
                <h4 style={{ color: '#fa8c16', marginBottom: 12 }}>
                  <ExclamationCircleOutlined style={{ marginRight: 8 }} />
                  异常检测 ({aiAnalysisResult.anomalies.length}项)
                </h4>
                {aiAnalysisResult.anomalies.map((anomaly, index) => (
                  <div
                    key={index}
                    style={{
                      padding: 12,
                      backgroundColor: '#fff7e6',
                      border: '1px solid #ffd591',
                      borderRadius: 6,
                      marginBottom: 8
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                      <Tag color={anomaly.severity === '高' ? 'red' : anomaly.severity === '中' ? 'orange' : 'yellow'}>
                        {anomaly.severity}
                      </Tag>
                      <span style={{ fontWeight: 'bold' }}>{anomaly.type}</span>
                    </div>
                    <div style={{ color: '#666' }}>{anomaly.description}</div>
                    {anomaly.time_range && (
                      <div style={{ fontSize: 12, color: '#999', marginTop: 4 }}>
                        时间范围: {anomaly.time_range}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* 优化建议 */}
            {aiAnalysisResult.recommendations && aiAnalysisResult.recommendations.length > 0 && (
              <div style={{ marginBottom: 24 }}>
                <h4 style={{ color: '#1890ff', marginBottom: 12 }}>
                  <BulbOutlined style={{ marginRight: 8 }} />
                  优化建议 ({aiAnalysisResult.recommendations.length}项)
                </h4>
                {aiAnalysisResult.recommendations.map((rec, index) => (
                  <div
                    key={index}
                    style={{
                      padding: 12,
                      backgroundColor: '#f0f5ff',
                      border: '1px solid #adc6ff',
                      borderRadius: 6,
                      marginBottom: 8
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                      <Tag color={rec.priority === '高' ? 'red' : rec.priority === '中' ? 'blue' : 'default'}>
                        {rec.priority}优先级
                      </Tag>
                      <span style={{ fontWeight: 'bold' }}>{rec.category}</span>
                    </div>
                    <div style={{ marginBottom: 4 }}>{rec.description}</div>
                    {rec.impact && (
                      <div style={{ fontSize: 12, color: '#1890ff' }}>
                        预期效果: {rec.impact}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* 关键洞察 */}
            {aiAnalysisResult.insights && aiAnalysisResult.insights.length > 0 && (
              <div>
                <h4 style={{ color: '#722ed1', marginBottom: 12 }}>
                  <BulbOutlined style={{ marginRight: 8 }} />
                  关键洞察
                </h4>
                <div style={{
                  padding: 16,
                  backgroundColor: '#f9f0ff',
                  border: '1px solid #d3adf7',
                  borderRadius: 6
                }}>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {aiAnalysisResult.insights.map((insight, index) => (
                      <li key={index} style={{ marginBottom: 4 }}>
                        {insight}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 保存结果模态框 */}
      <Modal
        title="保存分析结果"
        visible={saveModalVisible}
        onOk={confirmSave}
        onCancel={() => setSaveModalVisible(false)}
        confirmLoading={saving}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
            结果名称 <span style={{ color: 'red' }}>*</span>
          </label>
          <Input
            value={saveName}
            onChange={(e) => setSaveName(e.target.value)}
            placeholder="请输入结果名称"
            maxLength={100}
          />
        </div>
        <div>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
            结果描述
          </label>
          <Input.TextArea
            value={saveDescription}
            onChange={(e) => setSaveDescription(e.target.value)}
            placeholder="请输入结果描述（可选）"
            rows={4}
            maxLength={500}
          />
        </div>
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 4 }}>
          <div style={{ fontSize: 12, color: '#666' }}>
            <div>数据统计：{extractedData.length} 个文件</div>
            <div>分析时间：{new Date().toLocaleString('zh-CN')}</div>
            {analysisParams && (
              <>
                <div>主机ID：{analysisParams.hostId}</div>
                <div>容器名称：{analysisParams.containerName}</div>
              </>
            )}
          </div>
        </div>
      </Modal>



    </div>
  );
};

export default VTableAnalysisPage;
